package com.cursor.pressure.core.model;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 压力测试计划
 * 代表一个完整的压测任务，包含多个测试场景
 */
public class PressureTestPlan {
    
    private String id;
    private String name;
    private String description;
    private List<PressureTestScenario> scenarios;
    private Duration totalDuration;
    private boolean concurrent;
    private int threadCount;
    
    public PressureTestPlan() {
        this.id = UUID.randomUUID().toString();
        this.scenarios = new ArrayList<>();
    }
    
    public PressureTestPlan(String name) {
        this();
        this.name = name;
    }
    
    public void addScenario(PressureTestScenario scenario) {
        scenarios.add(scenario);
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<PressureTestScenario> getScenarios() {
        return scenarios;
    }

    public void setScenarios(List<PressureTestScenario> scenarios) {
        this.scenarios = scenarios;
    }

    public Duration getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(Duration totalDuration) {
        this.totalDuration = totalDuration;
    }

    public boolean isConcurrent() {
        return concurrent;
    }

    public void setConcurrent(boolean concurrent) {
        this.concurrent = concurrent;
    }

    public int getThreadCount() {
        return threadCount;
    }

    public void setThreadCount(int threadCount) {
        this.threadCount = threadCount;
    }
    
    @Override
    public String toString() {
        return "PressureTestPlan{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", scenarios=" + scenarios.size() +
                ", totalDuration=" + totalDuration +
                ", concurrent=" + concurrent +
                ", threadCount=" + threadCount +
                '}';
    }
} 