spring.application.name=demo1
### server port
server.port=8081

## logging
logging.level.org.springframework.security=INFO
logging.pattern.console=%d{dd-MM-yyyy HH:mm:ss} %magenta([%thread]) %highlight(%-5level) %logger.%M - %msg%n

## keycloak(?tom?????keycloak?realm)
spring.security.oauth2.client.provider.external.issuer-uri=http://localhost:8080/realms/myrealm

# external????
spring.security.oauth2.client.registration.external.provider=external
spring.security.oauth2.client.registration.external.client-name=myclient
spring.security.oauth2.client.registration.external.client-id=myclient
spring.security.oauth2.client.registration.external.client-secret=U8H2yI5Fph7NpHEjHoNzwXbb63leKbqf
spring.security.oauth2.client.registration.external.scope=openid,offline_access,profile
spring.security.oauth2.client.registration.external.authorization-grant-type=authorization_code