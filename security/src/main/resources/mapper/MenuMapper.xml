<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.et.security.mapper.MenuMapper">
  <resultMap id="BaseResultMap" type="com.et.security.entity.Menu">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 14 16:55:39 CST 2024.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pattern" jdbcType="VARCHAR" property="pattern" />
    <collection property="roles" ofType="com.et.security.entity.Role" >
      <id column="id" jdbcType="INTEGER" property="id" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="nameZh" jdbcType="VARCHAR" property="nameZh" />
    </collection>

  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 14 16:55:39 CST 2024.
    -->
    delete from menu
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.et.security.entity.Menu">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 14 16:55:39 CST 2024.
    -->
    insert into menu (id, pattern)
    values (#{id,jdbcType=INTEGER}, #{pattern,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.et.security.entity.Menu">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 14 16:55:39 CST 2024.
    -->
    update menu
    set pattern = #{pattern,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 14 16:55:39 CST 2024.
    -->
    select id, pattern
    from menu
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 14 16:55:39 CST 2024.
    -->
    select id, pattern
    from menu
  </select>
  <select id="getAllMenus" resultMap="BaseResultMap" >
    select * from menu m left join menu_role mr  on m.id =mr.mid left join `role` r  on r.id =mr.rid
  </select>

</mapper>