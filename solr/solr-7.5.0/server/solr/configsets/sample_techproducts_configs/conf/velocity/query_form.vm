#**
 *  Renders the main query form
 *#

<div class="query-box">
  <form id="query-form" action="#{url_for_home}" method="GET">

    <div class="inputs">
      <span #annTitle("Add the query using the &q= parameter")>
        Find:
        <input type="text" id="q" name="q" value="$!esc.html($params.get('q'))"/>
        <input type="submit" id="querySubmit"/>
        <input type="reset"/>
      </span>
      <div class="query-boost">
        <span #annTitle("Add the boost function &bf=price to the query")>
          <input type="checkbox" name="bf" value="price"
            #if($request.params.get('bf') == 'price')checked="true"#end
          >
            Boost by Price
          </input>
        </span>
      #parse("query_spatial.vm")
      #parse("query_group.vm")
      </div>
  </div>

    #if($request.params.get('debugQuery'))
      <input type="hidden" name="debugQuery" value="true"/>
    #end
    #if($annotate == true)
      <input type="hidden" name="annotateBrowse" value="true"/>
    #end
    #foreach($fq in $request.params.getParams('fq'))
      #if ($fq != "{!bbox}")
        <input type="hidden" name="fq" id="allFQs" value="$esc.html($fq)"/>
      #end
    #end

    <div class="constraints" #annTitle("Lists out the &fq filters.  Click to remove.")>
      #foreach($fq in $params.getParams('fq'))
        #set($previous_fq_count=$velocityCount - 1)
        #if($fq != '')
          &gt;
          <a style="{text-decoration: line-through;}"
            href="#url_for_filters($request.params.getParams('fq').subList(0,$previous_fq_count))"
          >$fq</a>
        #end
      #end
    </div>

    <div class="parsed_query_header">
      #if($request.params.get('debugQuery'))
        <a href="#" onclick='jQuery(this).siblings("div").toggle(); return false;'>toggle parsed query</a>
        <div class="parsed_query" style="display:none">$response.response.debug.parsedquery</div>
      #end
      #set($queryOpts = $request.params.get("queryOpts"))
      #if($queryOpts && $queryOpts != "")
        <input type="hidden" name="queryOpts" value="$queryOpts"/>
      #end
    </div>

  </form>
</div>
