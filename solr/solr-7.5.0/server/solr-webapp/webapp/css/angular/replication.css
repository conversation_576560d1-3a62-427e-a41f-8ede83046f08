/*

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

*/

#content #replication
{
  background-image: url( ../../img/div.gif );
  background-position: 21% 0;
  background-repeat: repeat-y;
}

#content #replication #frame
{
  float: right;
  width: 78%;
}

#content #replication #navigation
{
  border-right: 0;
  float: left;
  width: 20%;
}

#content #replication #error
{
  background-color: #f00;
  background-image: url( ../../img/ico/construction.png );
  background-position: 10px 50%;
  color: #fff;
  font-weight: bold;
  margin-bottom: 20px;
  padding: 10px;
  padding-left: 35px;
}

#content #replication .block
{
  border-bottom: 1px solid #c0c0c0;
  margin-bottom: 20px;
  padding-bottom: 20px;
}

#content #replication .block.last
{
  border-bottom: 0;
}

#content #replication .masterOnly,
#content #replication .slaveOnly
{
}

#content #replication.master .masterOnly
{
  display: block;
}

#content #replication.slave .slaveOnly
{
  display: block;
}

#content #replication .replicating
{
}

#content #replication.replicating .replicating
{
  display: block;
}

#content #replication #progress
{
  padding-bottom: 80px;
  position: relative;
}

#content #replication #progress .info
{
  padding: 5px;
}

#content #replication #progress #start
{
  margin-left: 100px;
  border-left: 1px solid #c0c0c0;
}

#content #replication #progress #bar
{
  background-color: #f0f0f0;
  margin-left: 100px;
  margin-right: 100px;
  position: relative;
}

#content #replication #progress #bar #bar-info,
#content #replication #progress #bar #eta
{
  position: absolute;
  right: -100px;
  width: 100px;
}

#content #replication #progress #bar #bar-info
{
  border-left: 1px solid #f0f0f0;
  margin-top: 30px;
}

#content #replication #progress #eta .info
{
  color: #4D4D4D;
  height: 30px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

#content #replication #progress #speed
{
  color: #4D4D4D;
  position: absolute;
  right: 100px;
  top: 0;
}

#content #replication #progress #bar #done
{
  background-color: #4D4D4D;
  box-shadow: 5px 5px 10px #c0c0c0;
  -moz-box-shadow: 5px 5px 10px #c0c0c0;
  -webkit-box-shadow: 5px 5px 10px #c0c0c0;
  height: 30px;
  position: relative;
}

#content #replication #progress #bar #done .percent
{
  font-weight: bold;
  height: 30px;
  line-height: 30px;
  padding-left: 5px;
  padding-right: 5px;
  position: absolute;
  right: 0;
  text-align: right;
}

#content #replication #progress #bar #done #done-info
{
  border-right: 1px solid #c0c0c0;
  position: absolute;
  right: 0;
  margin-top: 30px;
  text-align: right;
  width: 100px;
}

#content #replication #progress #bar #done #done-info .percent
{
  font-weight: bold;
}

#content #replication .block .label,
#content #replication #current-file .file,
#content #replication #current-file .progress,
#content #replication #iterations .iterations
{
  float: left;
}

#content #replication .block .label
{
  width: 100px;
}

#content #replication .block .label span
{
  display: block;
  padding-left: 21px;
}

#content #replication #current-file
{
  border-top: 1px solid #f0f0f0;
  margin-top: 10px;
  padding-top: 10px;
}

#content #replication #current-file .progress
{
  color: #4D4D4D;
  margin-left: 20px;
}

#content #replication #iterations .label span
{
  background-image: url( ../../img/ico/node-design.png );
}

#content #replication #iterations .iterations li
{
  background-position: 100% 50%;
  padding-right: 21px;
}

#content #replication #iterations .iterations.expanded li
{
  display: block;
}

#content #replication #iterations .iterations .latest
{
  display: block;
}

#content #replication #iterations .iterations .replicated
{
  color: #80c480;
}

#content #replication #iterations .iterations ul:hover .replicated,
#content #replication #iterations .iterations .replicated.latest
{
  color: #080;
}

#content #replication #iterations .iterations .replicated.latest
{
  background-image: url( ../../img/ico/tick.png );
}

#content #replication #iterations .iterations .failed
{
  color: #c48080;
}

#content #replication #iterations .iterations ul:hover .failed,
#content #replication #iterations .iterations .failed.latest
{
  color: #800;
}

#content #replication #iterations .iterations .failed.latest
{
  background-image: url( ../../img/ico/cross.png );
}

#content #replication #iterations .iterations a
{
  border-top: 1px solid #f0f0f0;
  margin-top: 2px;
  padding-top: 2px;
}

#content #replication #iterations .iterations a span
{
  background-position: 0 50%;
  color: #4D4D4D;
  padding-left: 21px;
}

#content #replication #iterations .iterations a span.expand
{
  background-image: url( ../../img/ico/chevron-small-expand.png );
  display: block;
}

#content #replication #iterations .iterations a span.collapse
{
  background-image: url( ../../img/ico/chevron-small.png );
  display: block;
}

#content #replication #details table
{
  margin-left: 20px;
  border-collapse: collapse;
}

#content #replication #details table th
{
  text-align: left;
}

#content #replication.slave #details table .slaveOnly
{
  display: table-row;
}

#content #replication #details table thead th
{
  color: #4D4D4D;
}

#content #replication #details table thead th,
#content #replication #details table tbody td
{
  padding-right: 20px;
}

#content #replication #details table thead td,
#content #replication #details table thead th,
#content #replication #details table tbody th,
#content #replication #details table tbody td div
{
  padding-top: 3px;
  padding-bottom: 3px;
}

#content #replication #details table tbody td,
#content #replication #details table tbody th
{
  border-top: 1px solid #f0f0f0;
}

#content #replication #details table thead td
{
  width: 100px;
}

#content #replication #details table thead td span
{
  background-image: url( ../../img/ico/clipboard-list.png );
  background-position: 0 50%;
  display: block;
  padding-left: 21px;
}

#content #replication #details table tbody th
{
  padding-right: 10px;
  text-align: right;
  white-space: nowrap;
}

#content #replication #details table tbody .size
{
  text-align: right;
  white-space: nowrap;
}

#content #replication #details table tbody .generation div
{
  text-align: center;
}

#content #replication #details table tbody .diff div
{
  background-color: #fcfcc9;
  padding-left: 1px;
  padding-right: 1px;
}

#content #replication .settings .label span
{
  background-image: url( ../../img/ico/hammer-screwdriver.png );
}

#content #replication .settings ul,
#content #replication .settings dl dt,
#content #replication .settings dl dd
{
  float: left;
}

#content #replication .settings ul li
{
  border-top: 1px solid #f0f0f0;
  padding-top: 3px;
  padding-top: 3px;
}

#content #replication .settings ul li:first-child
{
  border-top: 0;
  padding-top: 0;
}

#content #replication .settings dl dt
{
  clear: left;
  margin-right: 5px;
  width: 120px;
}

#content #replication .settings dl .ico
{
  background-position: 0 50%;
  padding-left: 21px;
}

#content #replication .settings dl .ico.ico-0
{
  background-image: url( ../../img/ico/slash.png );
}

#content #replication .settings dl .ico.ico-1
{
  background-image: url( ../../img/ico/tick.png );
}

#content #replication .timer
{
  box-shadow: 5px 5px 10px #c0c0c0;
  -moz-box-shadow: 5px 5px 10px #c0c0c0;
  -webkit-box-shadow: 5px 5px 10px #c0c0c0;
  margin-bottom: 20px;
  padding: 10px;
}

#content #replication .timer p,
#content #replication .timer small
{
  padding-left: 21px;
}

#content #replication .timer p
{
  background-image: url( ../../img/ico/clock-select-remain.png );
  background-position: 0 50%;
}

#content #replication .timer p .approx
{
  color: #4D4D4D;
  margin-right: 1px;
}

#content #replication .timer p .tick
{
  font-weight: bold;
}

#content #replication .timer small
{
  color: #4D4D4D;
}

#content #replication #navigation button
{
  display: block;
  margin-bottom: 10px;
}

#content #replication #navigation button.optional
{
}

#content #replication #navigation .replicate-now span
{
  background-image: url( ../../img/ico/document-convert.png );
}

#content #replication #navigation .abort-replication span
{
  background-image: url( ../../img/ico/hand.png );
}

#content #replication #navigation .disable-polling span
{
  background-image: url( ../../img/ico/cross.png );
}

#content #replication #navigation .enable-polling span
{
  background-image: url( ../../img/ico/tick.png );
}

#content #replication #navigation .disable-replication span
{
  background-image: url( ../../img/ico/cross.png );
}

#content #replication #navigation .enable-replication span
{
  background-image: url( ../../img/ico/tick.png );
}

#content #replication #navigation .refresh-status span
{
  background-image: url( ../../img/ico/arrow-circle.png );
}
