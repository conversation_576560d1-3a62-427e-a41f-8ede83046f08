/*

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

*/

#content #collections
{
  position: relative;
}

#content #collections #ui-block
{
  background-color: #fff;
  height: 200px;
  position: absolute;
  left: -5px;
  top: 35px;
  width: 500px;
}

#content #collections #frame
{
  float: right;
  width: 86%;
}

#content #collections #navigation
{
  padding-top: 50px;
  width: 12%;
}

#content #collections #navigation a
{
  padding-left: 5px;
}

#content #collections #frame .actions
{
  margin-bottom: 20px;
  min-height: 30px;
}

#content #collections .actions div.action
{
  width: 320px;
}

#content #collections .actions div.action .cloud
{
}

#content #collections .actions form .directory-note
{
  background-image: url( ../../img/ico/information-white.png );
  background-position: 22% 1px;
  color: #4D4D4D;
}

#content #collections .actions form .error
{
  background-image: url( ../../img/ico/cross-button.png );
  background-position: 22% 1px;
  color: #c00;
  font-weight: bold;
}

#content #collections .actions form p
{
  padding-bottom: 8px;
}

#content #collections .actions form label
{
  float: left;
  padding-top: 3px;
  padding-bottom: 3px;
  text-align: right;
  width: 25%;
}

#content #collections .actions form input,
#content #collections .actions form select,
#content #collections .actions form .chosen-container
#content #collections .actions form .buttons,
#content #collections .actions form .note span
{
  float: right;
  width: 71%;
}

#content #collections .actions form .note span
{
  padding-left: 3px;
  padding-right: 3px;
}

#content #collections .actions form .buttons
{
  padding-top: 10px;
}

#content #collections .actions form button.submit
{
  margin-right: 20px;
}

#content #collections .actions form button.submit span
{
  background-image: url( ../../img/ico/tick.png );
}

#content #collections .actions form button.reset span
{
  background-image: url( ../../img/ico/cross.png );
}

#content #collections .actions #add
{
  left: 0;
  position: absolute;
}

#content #collections .actions #add span
{
  background-image: url( ../../img/ico/plus-button.png );
}

#content #collections .actions #delete
{
  margin-right: 20px;
}

#content #collections .actions #delete span
{
  background-image: url( ../../img/ico/cross.png );
}

#content #collections .actions #reload span
{
  background-image: url( ../../img/ico/arrow-circle.png );
}

#content #collections .actions #rename span
{
  background-image: url( ../../img/ico/ui-text-field-select.png );
}

#content #collections .actions #create-alias span
{
  background-image: url( ../../img/ico/arrow-switch.png );
}

#content #collections .actions #delete-alias span
{
  background-image: url( ../../img/ico/cross-button.png );
}


#content #collections .actions div.action
{
  background-color: #fff;
  border: 1px solid #f0f0f0;
  box-shadow: 5px 5px 10px #c0c0c0;
  -moz-box-shadow: 5px 5px 10px #c0c0c0;
  -webkit-box-shadow: 5px 5px 10px #c0c0c0;
  position: absolute;
  left: 50px;
  top: 40px;
  padding: 10px;
}

#content #collections .actions #add-replica span
{
  background-image: url( ../../img/ico/plus-button.png );
}

#content #collections div.action.add-replica {
  border: 1px solid #f0f0f0;
  width: 400px;
  margin-right: 0px;
  padding: 10px;
  float: right;
}

#content #collections div.action.add-replica p {
  padding-bottom: 8px;
}

#content #collections div.action.add-replica .buttons {
  float: right;
}

#content #collections div.action.add-replica .buttons .submit span {
  background-image: url( ../../img/ico/tick.png );
  background-position: 0% 50%;
}

#content #collections div.action.add-replica .buttons .reset span {
  background-image: url( ../../img/ico/cross.png );
  background-position: 0% 50%;
}

#content #collections #data #collection-data h2 { background-image: url( ../../img/ico/box.png ); }
#content #collections #data #shard-data h2 { background-image: url( ../../img/ico/sitemap.png ); }
#content #collections #data #shard-data .replica h2 { background-image: url( ../../img/ico/node-slave.png ); }

#content #collections #data #index-data
{
  margin-top: 10px;
}

#content #collections #data li
{
  padding-bottom: 3px;
  padding-top: 3px;
}

#content #collections #data li.odd
{
  background-color: #f8f8f8;
}

#content #collections #data li dt
{
  float: left;
  width: 50%;
}

#content #collections #data li dd
{
  float: right;
  width: 50%;
}

#content #collections #data li dd.ico
{
  background-image: url( ../../img/ico/slash.png );
  height: 20px;
}

#content #collections #data li dd.ico.ico-1
{
  background-image: url( ../../img/ico/tick.png );
}

#content #collections #data li dd.ico span
{
}

#content #collections #add_advanced {
  background-image: url( ../../img/ico/chevron-small-expand.png );
  background-position: 100% 50%;
  cursor: pointer;
  padding-right: 21px;
}

#content #collections #add_advanced.open {
    background-image: url( ../../img/ico/chevron-small.png );
}

#content #collections .shard {
    margin-left: 40px;
}

#content #collections .replica {
    margin-left: 40px;
}

#content #collections .shard h2 span.openReplica {
  background-image: url( ../../img/ico/chevron-small-expand.png );
  background-position: 100% 50%;
  cursor: pointer;
  padding-right: 21px;
}

#content #collections .shard h2 span.openReplica .open {
  background-image: url( ../../img/ico/chevron-small.png );
}

#content #collections .replica h2 span {
  background-image: url( ../../img/ico/chevron-small-expand.png );
  background-position: 100% 50%;
  cursor: pointer;
  padding-right: 21px;
}

#content #collections .replica h2 span.rem {
  background-image: url( ../../img/ico/cross.png );
  background-position: 100% 50%;
  cursor: pointer;
  padding-right: 21px;
  right:10px;
}

#content #collections .shard h2 span.rem {
  background-image: url( ../../img/ico/cross.png );
  background-position: 100% 50%;
  cursor: pointer;
  padding-right: 21px;
  right:10px;
}

#content #collections .replica h2 span .open {
  background-image: url( ../../img/ico/chevron-small.png );
}

#content #collections #add-replica {
  float: right;
}

#content #collections .add select {
  width: 100%;
}

#content #collections .chosen-container ul {
    width: 100%;
    padding: 5px;
}

#content #collections .delete-replica span
{
  background-image: url( ../../img/ico/cross.png );
}
#content #collections .delete-replica button.submit span
{
  background-image: url( ../../img/ico/tick.png );
}

#content #collections .delete-shard span
{
  background-image: url( ../../img/ico/cross.png );
}
#content #collections .delete-shard button.submit span
{
  background-image: url( ../../img/ico/tick.png );
}

#content #collections #node-name .chosen-container
{
    width: 100% !important;
}

#content #collections #collection-data {
  float: left;
  width: 35%;
}

#content #collections #shard-data {
  float: left;
  width: 65%;
}
