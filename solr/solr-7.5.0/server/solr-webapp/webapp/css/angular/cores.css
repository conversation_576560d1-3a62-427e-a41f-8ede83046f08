/*

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

*/

#content #cores
{
  position: relative;
}

#content #cores #ui-block
{
  background-color: #fff;
  height: 200px;
  position: absolute;
  left: -5px;
  top: 35px;
  width: 500px;
}

#content #cores #frame
{
  float: right;
  width: 86%;
}

#content #cores #navigation
{
  padding-top: 50px;
  width: 12%;
}

#content #cores #navigation a
{
  padding-left: 5px;
}

#content #cores #frame .actions
{
  margin-bottom: 20px;
  min-height: 30px;
}

#content #cores .actions div.action
{
  width: 320px;
}

#content #cores .actions div.action .cloud
{
}

#content #cores .actions form .directory-note
{
  background-image: url( ../../img/ico/information-white.png );
  background-position: 22% 1px;
  color: #4D4D4D;
}

#content #cores .actions form .error
{
  background-image: url( ../../img/ico/cross-button.png );
  background-position: 22% 1px;
  color: #c00;
  font-weight: bold;
}

#content #cores .actions form p
{
  padding-bottom: 8px;
}

#content #cores .actions form label
{
  float: left;
  padding-top: 3px;
  padding-bottom: 3px;
  text-align: right;
  width: 25%;
}

#content #cores .actions form input,
#content #cores .actions form select,
#content #cores .actions form .buttons,
#content #cores .actions form .note span
{
  float: right;
  width: 71%;
}

#content #cores .actions form .note span
{
  padding-left: 3px;
  padding-right: 3px;
}

#content #cores .actions form .buttons
{
  padding-top: 10px;
}

#content #cores .actions form button.submit
{
  margin-right: 20px;
}

#content #cores .actions form button.submit span
{
  background-image: url( ../../img/ico/tick.png );
}

#content #cores .actions form button.reset span
{
  background-image: url( ../../img/ico/cross.png );
}

#content #cores .actions #add
{
  left: 0;
  position: absolute;
}

#content #cores .actions #add span
{
  background-image: url( ../../img/ico/plus-button.png );
}

#content #cores .actions #unload
{
  margin-right: 20px;
}

#content #cores .actions #unload span
{
  background-image: url( ../../img/ico/cross.png );
}

#content #cores .actions #reload span
{
  background-image: url( ../../img/ico/arrow-circle.png );
}

#content #cores .actions #rename span
{
  background-image: url( ../../img/ico/ui-text-field-select.png );
}

#content #cores .actions #swap span
{
  background-image: url( ../../img/ico/arrow-switch.png );
}


#content #cores .actions div.action
{
  background-color: #fff;
  border: 1px solid #f0f0f0;
  box-shadow: 5px 5px 10px #c0c0c0;
  -moz-box-shadow: 5px 5px 10px #c0c0c0;
  -webkit-box-shadow: 5px 5px 10px #c0c0c0;
  position: absolute;
  left: -50px;
  top: 40px;
  padding: 10px;
}

#content #cores #data #core-data h2 { background-image: url( ../../img/ico/box.png ); }
#content #cores #data #index-data h2 { background-image: url( ../../img/ico/chart.png ); }

#content #cores #data #index-data
{
  margin-top: 10px;
}

#content #cores #data li
{
  padding-bottom: 3px;
  padding-top: 3px;
}

#content #cores #data li.odd
{
  background-color: #f8f8f8;
}

#content #cores #data li dt
{
  float: left;
  width: 17%;
}

#content #cores #data li dd
{
  float: right;
  width: 82%;
}

#content #cores #data li dd.ico
{
  background-image: url( ../../img/ico/slash.png );
  height: 20px;
}

#content #cores #data li dd.ico.ico-1
{
  background-image: url( ../../img/ico/tick.png );
}

#content #cores #data li dd.ico span
{
}
