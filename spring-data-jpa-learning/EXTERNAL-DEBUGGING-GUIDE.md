# 外部软件调试指南 - Spring Data JPA 多表查询

## 🚀 快速开始

### 1. 启动应用
```bash
cd spring-data-jpa-learning
mvn spring-boot:run
```

### 2. 验证服务状态
```bash
curl http://localhost:8080/api/users
```

## 📱 Postman 调试

### 导入API集合

#### 方式1：导入Collection文件
1. 打开Postman
2. 点击 `Import` → `File`
3. 选择：`postman/Spring-Data-JPA-Learning.postman_collection.json`

#### 方式2：通过OpenAPI导入
1. 打开Postman
2. 点击 `Import` → `Link`
3. 输入：`http://localhost:8080/v3/api-docs`

### 环境变量设置
```json
{
  "baseUrl": "http://localhost:8080",
  "apiPath": "/api/users"
}
```

## 🔍 多表查询API调试示例

### 1. 基础用户查询
```bash
# 获取所有用户
GET http://localhost:8080/api/users

# 根据ID获取用户
GET http://localhost:8080/api/users/1

# 根据用户名查找
GET http://localhost:8080/api/users/username/admin
```

### 2. 用户订单关联查询
```bash
# 获取用户的所有订单（一对多查询）
GET http://localhost:8080/api/orders/user/1

# 根据用户名查找订单（跨表查询）
GET http://localhost:8080/api/orders/user/username/admin

# 获取用户订单统计
GET http://localhost:8080/api/orders/user/1/statistics
```

### 3. 商品相关查询
```bash
# 获取所有商品
GET http://localhost:8080/api/products

# 根据分类查询商品
GET http://localhost:8080/api/products/category/ELECTRONICS

# 查询用户购买过的商品（多对多查询）
GET http://localhost:8080/api/products/purchased-by-user/1

# 查询热销商品
GET http://localhost:8080/api/products/best-selling
```

### 4. 订单项复杂查询
```bash
# 获取用户的所有订单项（多层关联）
GET http://localhost:8080/api/order-items/user/1

# 查询商品的销售记录
GET http://localhost:8080/api/order-items/product/1

# 查询经常一起购买的商品
GET http://localhost:8080/api/order-items/frequently-bought-together/1
```

### 5. 聚合统计查询
```bash
# 用户购买统计
GET http://localhost:8080/api/users/1/purchase-statistics

# 商品销售统计
GET http://localhost:8080/api/products/1/sales-statistics

# 月度销售报告
GET http://localhost:8080/api/orders/monthly-sales-report
```

## 🌐 其他外部工具调试

### 1. Insomnia
1. 打开Insomnia
2. 点击 `Create` → `Import From` → `URL`
3. 输入：`http://localhost:8080/v3/api-docs`

### 2. Thunder Client (VS Code)
1. 安装Thunder Client扩展
2. 点击 `Import` → `From OpenAPI`
3. 输入：`http://localhost:8080/v3/api-docs`

### 3. curl命令行调试
```bash
# 基础查询
curl -X GET "http://localhost:8080/api/users" -H "accept: application/json"

# 带参数查询
curl -X GET "http://localhost:8080/api/users/filter?active=true&minAge=20&maxAge=40" \
     -H "accept: application/json"

# POST创建数据
curl -X POST "http://localhost:8080/api/users" \
     -H "accept: application/json" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "testuser",
       "email": "<EMAIL>",
       "realName": "测试用户",
       "age": 25,
       "active": true
     }'
```

## 🗄️ 数据库调试

### H2控制台访问
1. 访问：http://localhost:8080/h2-console
2. 连接信息：
   - JDBC URL: `jdbc:h2:mem:testdb`
   - 用户名: `sa`
   - 密码: (空)

### 常用SQL查询
```sql
-- 查看所有表
SHOW TABLES;

-- 查看用户数据
SELECT * FROM users LIMIT 10;

-- 查看订单数据
SELECT * FROM orders LIMIT 10;

-- 查看商品数据
SELECT * FROM products LIMIT 10;

-- 查看订单项数据
SELECT * FROM order_items LIMIT 10;

-- 多表关联查询示例
SELECT u.username, o.order_number, o.total_amount, o.status
FROM users u 
JOIN orders o ON u.id = o.user_id 
ORDER BY o.created_at DESC;

-- 用户订单统计
SELECT u.username, 
       COUNT(o.id) as order_count,
       SUM(o.total_amount) as total_spent
FROM users u 
LEFT JOIN orders o ON u.id = o.user_id 
GROUP BY u.id, u.username 
ORDER BY total_spent DESC;

-- 商品销售统计
SELECT p.name, 
       SUM(oi.quantity) as total_sold,
       SUM(oi.subtotal) as total_revenue
FROM products p 
JOIN order_items oi ON p.id = oi.product_id
JOIN orders o ON oi.order_id = o.id
WHERE o.status != 'CANCELLED'
GROUP BY p.id, p.name 
ORDER BY total_revenue DESC;
```

## 🔧 调试技巧

### 1. 查看SQL日志
应用配置了SQL日志输出，启动时可以看到：
```
Hibernate: select user0_.id as id1_3_, user0_.active as active2_3_, ...
```

### 2. 使用Swagger UI交互式调试
1. 访问：http://localhost:8080/swagger-ui.html
2. 选择API接口
3. 点击 `Try it out`
4. 输入参数
5. 点击 `Execute`
6. 查看响应结果

### 3. 分页查询调试
```bash
# 分页查询用户
GET http://localhost:8080/api/users/page?page=0&size=5&sortBy=id&sortDirection=asc

# 分页查询订单
GET http://localhost:8080/api/orders/page?page=0&size=10&sortBy=createdAt&sortDirection=desc
```

### 4. 复杂筛选查询调试
```bash
# 多条件筛选用户
GET http://localhost:8080/api/users/filter?active=true&minAge=20&maxAge=40&page=0&size=10

# 多条件筛选订单
GET http://localhost:8080/api/orders/filter?status=DELIVERED&minAmount=100&maxAmount=10000
```

## 📊 测试数据说明

### 用户数据
- 总计：36个用户
- 包含中英文用户名
- 不同年龄段和状态

### 订单数据
- 总计：17个订单
- 不同状态：PENDING, PAID, SHIPPED, DELIVERED, CANCELLED
- 时间跨度：最近30天

### 商品数据
- 总计：30个商品
- 6个分类：ELECTRONICS, CLOTHING, BOOKS, HOME, SPORTS, FOOD
- 不同价格区间和库存状态

### 订单项数据
- 建立了订单和商品的多对多关系
- 包含数量、价格、小计等信息

## 🚨 常见问题解决

### 1. 连接被拒绝
- 确认应用已启动：`mvn spring-boot:run`
- 检查端口是否被占用：`netstat -an | findstr 8080`

### 2. 404错误
- 检查API路径是否正确
- 确认应用启动完成（看到"Started SpringDataJpaLearningApplication"）

### 3. 数据为空
- 检查H2控制台确认数据已初始化
- 重启应用重新加载测试数据

### 4. 查询超时
- 检查查询参数是否合理
- 查看应用日志确认SQL执行情况

## 📝 调试检查清单

- [ ] 应用已启动并运行在8080端口
- [ ] 可以访问Swagger UI
- [ ] H2数据库控制台可以连接
- [ ] 测试数据已正确加载
- [ ] Postman/其他工具已导入API集合
- [ ] 环境变量已正确配置
- [ ] 基础API调用成功

通过这些外部工具，您可以全面测试和调试Spring Data JPA的多表查询功能！
