package com.example.jpa.entity;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.persistence.*;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 商品实体类 - Spring Data JPA 多表查询学习示例
 * 
 * 演示的JPA特性：
 * - 基本字段映射
 * - 索引配置
 * - 枚举类型映射
 * - 一对多关系（商品 -> 订单项）
 * - 数据验证注解
 * 
 * <AUTHOR> Learning
 */
@Entity
@Table(name = "products",
       indexes = {
           @Index(name = "idx_product_name", columnList = "name"),
           @Index(name = "idx_product_category", columnList = "category"),
           @Index(name = "idx_product_status", columnList = "status"),
           @Index(name = "idx_product_price", columnList = "price")
       })
@Schema(description = "商品实体", example = "{\n" +
        "    \"id\": 1,\n" +
        "    \"name\": \"iPhone 15 Pro\",\n" +
        "    \"description\": \"苹果最新款智能手机\",\n" +
        "    \"price\": 7999.00,\n" +
        "    \"stock\": 100,\n" +
        "    \"category\": \"ELECTRONICS\",\n" +
        "    \"status\": \"ACTIVE\",\n" +
        "    \"createdAt\": \"2025-07-09T10:00:00\"\n" +
        "}")
public class Product {

    /**
     * 商品分类枚举
     */
    public enum ProductCategory {
        ELECTRONICS("电子产品"),
        CLOTHING("服装"),
        BOOKS("图书"),
        HOME("家居用品"),
        SPORTS("体育用品"),
        FOOD("食品"),
        OTHER("其他");

        private final String description;

        ProductCategory(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 商品状态枚举
     */
    public enum ProductStatus {
        ACTIVE("上架"),
        INACTIVE("下架"),
        OUT_OF_STOCK("缺货");

        private final String description;

        ProductStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 主键ID - 自动生成
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "商品ID", example = "1", accessMode = Schema.AccessMode.READ_ONLY)
    private Long id;

    /**
     * 商品名称
     */
    @Column(name = "name", nullable = false, length = 200)
    @NotBlank(message = "商品名称不能为空")
    @Size(max = 200, message = "商品名称长度不能超过200个字符")
    @Schema(description = "商品名称", example = "iPhone 15 Pro", required = true, maxLength = 200)
    private String name;

    /**
     * 商品描述
     */
    @Column(name = "description", length = 1000)
    @Size(max = 1000, message = "商品描述长度不能超过1000个字符")
    @Schema(description = "商品描述", example = "苹果最新款智能手机，配备A17 Pro芯片", maxLength = 1000)
    private String description;

    /**
     * 商品价格
     */
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    @NotNull(message = "商品价格不能为空")
    @DecimalMin(value = "0.01", message = "商品价格必须大于0")
    @Schema(description = "商品价格", example = "7999.00", required = true)
    private BigDecimal price;

    /**
     * 库存数量
     */
    @Column(name = "stock", nullable = false)
    @NotNull(message = "库存数量不能为空")
    @Schema(description = "库存数量", example = "100", required = true)
    private Integer stock = 0;

    /**
     * 商品分类
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false, length = 20)
    @NotNull(message = "商品分类不能为空")
    @Schema(description = "商品分类", example = "ELECTRONICS", required = true,
            allowableValues = {"ELECTRONICS", "CLOTHING", "BOOKS", "HOME", "SPORTS", "FOOD", "OTHER"})
    private ProductCategory category;

    /**
     * 商品状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @NotNull(message = "商品状态不能为空")
    @Schema(description = "商品状态", example = "ACTIVE", required = true,
            allowableValues = {"ACTIVE", "INACTIVE", "OUT_OF_STOCK"})
    private ProductStatus status = ProductStatus.ACTIVE;

    /**
     * 商品图片URL
     */
    @Column(name = "image_url", length = 500)
    @Size(max = 500, message = "图片URL长度不能超过500个字符")
    @Schema(description = "商品图片URL", example = "https://example.com/images/iphone15pro.jpg", maxLength = 500)
    private String imageUrl;

    /**
     * 一对多关系：一个商品可以在多个订单项中
     * - mappedBy：指定关联的属性名
     * - fetch = FetchType.LAZY：懒加载
     */
    @OneToMany(mappedBy = "product", fetch = FetchType.LAZY)
    @JsonManagedReference("product-orderItems")
    @Schema(description = "订单项列表", accessMode = Schema.AccessMode.READ_ONLY)
    private Set<OrderItem> orderItems = new HashSet<>();

    /**
     * 创建时间 - 自动设置
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @Schema(description = "创建时间", example = "2025-07-09T10:00:00", 
            accessMode = Schema.AccessMode.READ_ONLY)
    private LocalDateTime createdAt;

    /**
     * 更新时间 - 自动更新
     */
    @Column(name = "updated_at")
    @Schema(description = "更新时间", example = "2025-07-09T10:00:00", 
            accessMode = Schema.AccessMode.READ_ONLY)
    private LocalDateTime updatedAt;

    /**
     * JPA要求的无参构造函数
     */
    public Product() {
    }

    /**
     * 便于创建商品的构造函数
     */
    public Product(String name, String description, BigDecimal price, Integer stock, ProductCategory category) {
        this.name = name;
        this.description = description;
        this.price = price;
        this.stock = stock;
        this.category = category;
        this.status = ProductStatus.ACTIVE;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * JPA生命周期回调 - 持久化前执行
     */
    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * JPA生命周期回调 - 更新前执行
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否有库存
     */
    public boolean hasStock() {
        return stock != null && stock > 0;
    }

    /**
     * 检查是否可以购买指定数量
     */
    public boolean canPurchase(int quantity) {
        return hasStock() && stock >= quantity && status == ProductStatus.ACTIVE;
    }

    /**
     * 减少库存
     */
    public void reduceStock(int quantity) {
        if (stock >= quantity) {
            this.stock -= quantity;
            if (this.stock == 0) {
                this.status = ProductStatus.OUT_OF_STOCK;
            }
        } else {
            throw new IllegalArgumentException("库存不足，当前库存：" + stock + "，需要：" + quantity);
        }
    }

    /**
     * 增加库存
     */
    public void addStock(int quantity) {
        this.stock += quantity;
        if (this.status == ProductStatus.OUT_OF_STOCK && this.stock > 0) {
            this.status = ProductStatus.ACTIVE;
        }
    }

    // ========== Getters and Setters ==========

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public ProductCategory getCategory() {
        return category;
    }

    public void setCategory(ProductCategory category) {
        this.category = category;
    }

    public ProductStatus getStatus() {
        return status;
    }

    public void setStatus(ProductStatus status) {
        this.status = status;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Set<OrderItem> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(Set<OrderItem> orderItems) {
        this.orderItems = orderItems;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Product product = (Product) o;
        return Objects.equals(id, product.id) &&
               Objects.equals(name, product.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name);
    }

    @Override
    public String toString() {
        return "Product{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", price=" + price +
                ", stock=" + stock +
                ", category=" + category +
                ", status=" + status +
                ", imageUrl='" + imageUrl + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
