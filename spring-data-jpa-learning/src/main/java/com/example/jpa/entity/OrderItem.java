package com.example.jpa.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.persistence.*;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 订单项实体类 - Spring Data JPA 多表查询学习示例
 * 
 * 演示的JPA关联特性：
 * - 多对多关系的中间表实现
 * - @ManyToOne：多对一关系（订单项 -> 订单，订单项 -> 商品）
 * - 复合主键的替代方案（使用独立主键）
 * - 关联表的额外字段（数量、价格等）
 * 
 * 这个类实现了Order和Product之间的多对多关系：
 * - 一个订单可以包含多个商品
 * - 一个商品可以出现在多个订单中
 * - 订单项记录了具体的数量和价格信息
 * 
 * <AUTHOR> Learning
 */
@Entity
@Table(name = "order_items",
       indexes = {
           @Index(name = "idx_order_item_order_id", columnList = "order_id"),
           @Index(name = "idx_order_item_product_id", columnList = "product_id"),
           @Index(name = "idx_order_item_order_product", columnList = "order_id, product_id", unique = true)
       })
@Schema(description = "订单项实体", example = "{\n" +
        "    \"id\": 1,\n" +
        "    \"quantity\": 2,\n" +
        "    \"price\": 7999.00,\n" +
        "    \"subtotal\": 15998.00,\n" +
        "    \"orderId\": 1,\n" +
        "    \"productId\": 1,\n" +
        "    \"createdAt\": \"2025-07-09T10:00:00\"\n" +
        "}")
public class OrderItem {

    /**
     * 主键ID - 自动生成
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "订单项ID", example = "1", accessMode = Schema.AccessMode.READ_ONLY)
    private Long id;

    /**
     * 商品数量
     */
    @Column(name = "quantity", nullable = false)
    @NotNull(message = "商品数量不能为空")
    @Min(value = 1, message = "商品数量必须大于0")
    @Schema(description = "商品数量", example = "2", required = true)
    private Integer quantity;

    /**
     * 商品单价（下单时的价格，可能与当前商品价格不同）
     */
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    @NotNull(message = "商品价格不能为空")
    @DecimalMin(value = "0.01", message = "商品价格必须大于0")
    @Schema(description = "商品单价", example = "7999.00", required = true)
    private BigDecimal price;

    /**
     * 小计金额（数量 × 单价）
     */
    @Column(name = "subtotal", nullable = false, precision = 10, scale = 2)
    @Schema(description = "小计金额", example = "15998.00", accessMode = Schema.AccessMode.READ_ONLY)
    private BigDecimal subtotal;

    /**
     * 多对一关系：订单项属于一个订单
     * - fetch = FetchType.LAZY：懒加载
     * - @JoinColumn：指定外键列名
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false, foreignKey = @ForeignKey(name = "fk_order_item_order"))
    @JsonBackReference("order-orderItems")
    @NotNull(message = "订单不能为空")
    @Schema(description = "所属订单", accessMode = Schema.AccessMode.READ_ONLY)
    private Order order;

    /**
     * 多对一关系：订单项对应一个商品
     * - fetch = FetchType.LAZY：懒加载
     * - @JoinColumn：指定外键列名
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", nullable = false, foreignKey = @ForeignKey(name = "fk_order_item_product"))
    @JsonBackReference("product-orderItems")
    @NotNull(message = "商品不能为空")
    @Schema(description = "对应商品", accessMode = Schema.AccessMode.READ_ONLY)
    private Product product;

    /**
     * 创建时间 - 自动设置
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @Schema(description = "创建时间", example = "2025-07-09T10:00:00", 
            accessMode = Schema.AccessMode.READ_ONLY)
    private LocalDateTime createdAt;

    /**
     * 更新时间 - 自动更新
     */
    @Column(name = "updated_at")
    @Schema(description = "更新时间", example = "2025-07-09T10:00:00", 
            accessMode = Schema.AccessMode.READ_ONLY)
    private LocalDateTime updatedAt;

    /**
     * JPA要求的无参构造函数
     */
    public OrderItem() {
    }

    /**
     * 便于创建订单项的构造函数
     */
    public OrderItem(Order order, Product product, Integer quantity, BigDecimal price) {
        this.order = order;
        this.product = product;
        this.quantity = quantity;
        this.price = price;
        this.subtotal = price.multiply(BigDecimal.valueOf(quantity));
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * JPA生命周期回调 - 持久化前执行
     */
    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        calculateSubtotal();
    }

    /**
     * JPA生命周期回调 - 更新前执行
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
        calculateSubtotal();
    }

    // ========== 便利方法 ==========

    /**
     * 计算小计金额
     */
    public void calculateSubtotal() {
        if (price != null && quantity != null) {
            this.subtotal = price.multiply(BigDecimal.valueOf(quantity));
        }
    }

    /**
     * 更新数量并重新计算小计
     */
    public void updateQuantity(Integer newQuantity) {
        this.quantity = newQuantity;
        calculateSubtotal();
    }

    /**
     * 更新价格并重新计算小计
     */
    public void updatePrice(BigDecimal newPrice) {
        this.price = newPrice;
        calculateSubtotal();
    }

    /**
     * 获取商品名称（便利方法，避免懒加载异常）
     */
    public String getProductName() {
        return product != null ? product.getName() : null;
    }

    /**
     * 获取订单编号（便利方法，避免懒加载异常）
     */
    public String getOrderNumber() {
        return order != null ? order.getOrderNumber() : null;
    }

    // ========== Getters and Setters ==========

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
        calculateSubtotal();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
        calculateSubtotal();
    }

    public BigDecimal getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderItem orderItem = (OrderItem) o;
        return Objects.equals(id, orderItem.id) &&
               Objects.equals(order, orderItem.order) &&
               Objects.equals(product, orderItem.product);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, order, product);
    }

    @Override
    public String toString() {
        return "OrderItem{" +
                "id=" + id +
                ", quantity=" + quantity +
                ", price=" + price +
                ", subtotal=" + subtotal +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
