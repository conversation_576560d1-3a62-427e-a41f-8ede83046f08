package com.example.jpa.service;

import com.example.jpa.entity.User;
import com.example.jpa.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户服务类 - Spring Data JPA 学习示例
 * 
 * 演示的Spring特性：
 * - @Service注解标记服务层
 * - @Transactional事务管理
 * - 依赖注入Repository
 * - 业务逻辑封装
 * - 异常处理
 * 
 * <AUTHOR> Learning
 */
@Service
@Transactional(readOnly = true)
public class UserService {

    private final UserRepository userRepository;

    @Autowired
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    // ========== 基本CRUD操作 ==========

    /**
     * 创建新用户
     * @Transactional 写操作需要事务
     */
    @Transactional
    public User createUser(User user) {
        // 检查用户名是否已存在
        if (userRepository.findByUsername(user.getUsername()).isPresent()) {
            throw new IllegalArgumentException("用户名已存在: " + user.getUsername());
        }
        
        // 检查邮箱是否已存在
        if (userRepository.findByEmail(user.getEmail()).isPresent()) {
            throw new IllegalArgumentException("邮箱已存在: " + user.getEmail());
        }
        
        return userRepository.save(user);
    }

    /**
     * 根据ID查找用户
     */
    public Optional<User> findUserById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * 根据用户名查找用户
     */
    public Optional<User> findUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 根据邮箱查找用户
     */
    public Optional<User> findUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    /**
     * 获取所有用户
     */
    public List<User> findAllUsers() {
        return userRepository.findAll();
    }

    // ========== EntityGraph 解决N+1问题的方法 ==========

    /**
     * 获取所有用户及其订单（使用EntityGraph避免N+1问题）
     * 一次性加载用户和订单数据，只执行一条SQL
     */
    public List<User> findAllUsersWithOrders() {
        return userRepository.findAllWithOrders();
    }

    /**
     * 根据ID获取用户及其订单（使用EntityGraph）
     */
    public Optional<User> findUserWithOrdersById(Long id) {
        return userRepository.findWithOrdersById(id);
    }

    /**
     * 分页获取用户及其订单（使用EntityGraph）
     */
    public Page<User> findUsersWithOrders(int page, int size, String sortBy, String sortDirection) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDirection), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        return userRepository.findAllWithOrders(pageable);
    }

    /**
     * 获取激活用户及其订单（使用EntityGraph）
     */
    public List<User> findActiveUsersWithOrders() {
        return userRepository.findByActiveWithOrders(true);
    }

    /**
     * 获取用户及其订单和订单项（深度关联）
     */
    public Optional<User> findUserWithOrdersAndItemsById(Long id) {
        return userRepository.findWithOrdersAndItemsById(id);
    }

    // ========== 完整EntityGraph 解决所有N+1问题 ==========

    /**
     * 获取所有用户的完整数据（用户、订单、订单项、商品）
     * 使用完整EntityGraph，彻底解决所有嵌套的N+1问题
     */
    public List<User> findAllUsersWithCompleteData() {
        return userRepository.findAllWithCompleteData();
    }

    /**
     * 根据ID获取用户的完整数据
     */
    public Optional<User> findUserWithCompleteDataById(Long id) {
        return userRepository.findWithCompleteDataById(id);
    }

    /**
     * 分页获取用户的完整数据
     */
    public Page<User> findUsersWithCompleteData(int page, int size, String sortBy, String sortDirection) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDirection), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        return userRepository.findAllWithCompleteData(pageable);
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public User updateUser(Long id, User userDetails) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在，ID: " + id));

        // 检查用户名是否被其他用户使用
        if (!user.getUsername().equals(userDetails.getUsername())) {
            Optional<User> existingUser = userRepository.findByUsername(userDetails.getUsername());
            if (existingUser.isPresent() && !existingUser.get().getId().equals(id)) {
                throw new IllegalArgumentException("用户名已被其他用户使用: " + userDetails.getUsername());
            }
        }

        // 检查邮箱是否被其他用户使用
        if (!user.getEmail().equals(userDetails.getEmail())) {
            Optional<User> existingUser = userRepository.findByEmail(userDetails.getEmail());
            if (existingUser.isPresent() && !existingUser.get().getId().equals(id)) {
                throw new IllegalArgumentException("邮箱已被其他用户使用: " + userDetails.getEmail());
            }
        }

        // 更新用户信息
        user.setUsername(userDetails.getUsername());
        user.setEmail(userDetails.getEmail());
        user.setRealName(userDetails.getRealName());
        user.setAge(userDetails.getAge());
        user.setActive(userDetails.getActive());

        return userRepository.save(user);
    }

    /**
     * 删除用户
     */
    @Transactional
    public void deleteUser(Long id) {
        if (!userRepository.existsById(id)) {
            throw new IllegalArgumentException("用户不存在，ID: " + id);
        }
        userRepository.deleteById(id);
    }

    // ========== 高级查询操作 ==========

    /**
     * 分页查询用户
     */
    public Page<User> findUsersWithPagination(int page, int size, String sortBy, String sortDirection) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDirection), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        return userRepository.findAll(pageable);
    }

    /**
     * 根据年龄范围查找用户
     */
    public List<User> findUsersByAgeRange(Integer minAge, Integer maxAge) {
        return userRepository.findByAgeBetween(minAge, maxAge);
    }

    /**
     * 查找激活用户
     */
    public List<User> findActiveUsers() {
        return userRepository.findByActive(true);
    }

    /**
     * 分页查询激活用户
     */
    public Page<User> findActiveUsersWithPagination(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        return userRepository.findByActive(true, pageable);
    }

    /**
     * 根据真实姓名模糊查询
     */
    public List<User> findUsersByRealNameContaining(String realName) {
        return userRepository.findByRealNameContaining(realName);
    }

    /**
     * 查找最近注册的用户
     */
    public List<User> findRecentUsers(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return userRepository.findRecentUsers(pageable);
    }

    /**
     * 多条件筛选查询用户
     */
    public Page<User> findUsersWithFilters(String username, String email, Boolean active, 
                                          Integer minAge, Integer maxAge, 
                                          int page, int size, String sortBy, String sortDirection) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDirection), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        return userRepository.findUsersWithFilters(username, email, active, minAge, maxAge, pageable);
    }

    // ========== 批量操作 ==========

    /**
     * 批量创建用户
     */
    @Transactional
    public List<User> createUsers(List<User> users) {
        // 验证用户名和邮箱的唯一性
        for (User user : users) {
            if (userRepository.findByUsername(user.getUsername()).isPresent()) {
                throw new IllegalArgumentException("用户名已存在: " + user.getUsername());
            }
            if (userRepository.findByEmail(user.getEmail()).isPresent()) {
                throw new IllegalArgumentException("邮箱已存在: " + user.getEmail());
            }
        }
        
        return userRepository.saveAll(users);
    }

    /**
     * 批量更新用户激活状态
     */
    @Transactional
    public int updateActiveStatusByAge(Boolean active, Integer age) {
        return userRepository.updateActiveStatusByAge(active, age);
    }

    /**
     * 清理非激活用户
     */
    @Transactional
    public int cleanupInactiveUsers(int daysAgo) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysAgo);
        return userRepository.deleteInactiveUsersBefore(cutoffDate);
    }

    // ========== 统计操作 ==========

    /**
     * 获取用户总数
     */
    public long getTotalUserCount() {
        return userRepository.count();
    }

    /**
     * 获取激活用户数量
     */
    public long getActiveUserCount() {
        return userRepository.countActiveUsers();
    }

    /**
     * 获取用户年龄统计信息
     */
    public String getAgeStatistics() {
        Object[] stats = userRepository.getAgeStatistics();
        if (stats != null && stats.length == 3) {
            Double avgAge = (Double) stats[0];
            Integer minAge = (Integer) stats[1];
            Integer maxAge = (Integer) stats[2];
            
            return String.format("年龄统计 - 平均: %.1f, 最小: %d, 最大: %d", 
                               avgAge != null ? avgAge : 0.0, 
                               minAge != null ? minAge : 0, 
                               maxAge != null ? maxAge : 0);
        }
        return "暂无年龄统计数据";
    }

    /**
     * 检查用户名是否可用
     */
    public boolean isUsernameAvailable(String username) {
        return !userRepository.findByUsername(username).isPresent();
    }

    /**
     * 检查邮箱是否可用
     */
    public boolean isEmailAvailable(String email) {
        return !userRepository.findByEmail(email).isPresent();
    }
}
