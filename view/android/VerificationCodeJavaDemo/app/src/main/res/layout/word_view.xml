<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:gravity="center"
    android:orientation="vertical">


    <FrameLayout
        android:id="@+id/word_fl_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/word_iv_cover"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleType="fitXY" />

        <View
            android:id="@+id/word_v_flash"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/drag_flash" />
    </FrameLayout>

</LinearLayout>