<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/drag_fl_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal">

        <ImageView
            android:id="@+id/drag_iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY" />

        <ImageView
            android:id="@+id/drag_iv_block"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitXY" />

        <View
            android:id="@+id/drag_v_flash"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/drag_flash" />


        <com.example.verificationcodejavademo.widget.DiyStyleTextView
            android:id="@+id/drag_tv_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="#bbf2ece1"
            android:gravity="center_vertical"
            android:paddingLeft="12dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:text="你本次用了1.0秒"
            android:textColor="#323232"
            android:textSize="14dp" />

    </FrameLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="6dp">

        <SeekBar
            android:id="@+id/drag_sb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:progressDrawable="@drawable/drag_seek_progress"
            android:splitTrack="false"
            android:thumb="@drawable/drag_btn_error"
            android:thumbOffset="0dp" />

        <TextView
            android:id="@+id/drag_tv_tips2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="向右拖动滑块填充拼图"
            android:textSize="16dp" />

    </FrameLayout>
</LinearLayout>