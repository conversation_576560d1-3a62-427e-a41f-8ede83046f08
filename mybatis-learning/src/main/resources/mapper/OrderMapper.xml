<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "https://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.mybatis.mapper.OrderMapper">

    <!-- 订单结果映射 -->
    <resultMap id="orderResultMap" type="Order">
        <id property="id" column="id"/>
        <result property="orderNumber" column="order_number"/>
        <result property="userId" column="user_id"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="status" column="status"/>
        <result property="orderDate" column="order_date"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 订单与用户关联结果映射 -->
    <resultMap id="orderWithUserResultMap" type="Order" extends="orderResultMap">
        <association property="user" column="user_id"
                    select="com.example.mybatis.mapper.UserMapper.findUserById"
                    fetchType="eager"/>
    </resultMap>

    <!-- 订单与订单项关联结果映射 -->
    <resultMap id="orderWithItemsResultMap" type="Order" extends="orderResultMap">
        <collection property="orderItems" column="id"
                   select="com.example.mybatis.mapper.OrderItemMapper.findOrderItemsByOrderId"
                   fetchType="eager"/>
    </resultMap>

    <!-- 订单完整信息结果映射 -->
    <resultMap id="orderWithDetailsResultMap" type="Order" extends="orderResultMap">
        <association property="user" column="user_id"
                    select="com.example.mybatis.mapper.UserMapper.findUserById"
                    fetchType="eager"/>
        <collection property="orderItems" column="id"
                   select="com.example.mybatis.mapper.OrderItemMapper.findOrderItemsByOrderId"
                   fetchType="eager"/>
    </resultMap>

    <!-- 可重用的SQL片段 -->
    <sql id="orderColumns">
        id, order_number, user_id, total_amount, status, order_date, created_at, updated_at
    </sql>

    <sql id="orderInsertColumns">
        order_number, user_id, total_amount, status, order_date, created_at, updated_at
    </sql>

    <!-- 动态WHERE条件 -->
    <sql id="orderWhereCondition">
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                AND order_number = #{orderNumber}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="totalAmount != null">
                AND total_amount = #{totalAmount}
            </if>
        </where>
    </sql>

    <!-- 查询订单完整信息 -->
    <select id="findOrderWithDetails" parameterType="Long" resultMap="orderWithDetailsResultMap">
        SELECT <include refid="orderColumns"/>
        FROM orders
        WHERE id = #{id}
    </select>

    <!-- 批量插入订单 -->
    <insert id="batchInsertOrders" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO orders (<include refid="orderInsertColumns"/>)
        VALUES
        <foreach collection="orders" item="order" separator=",">
            (#{order.orderNumber}, #{order.userId}, #{order.totalAmount}, 
             #{order.status}, #{order.orderDate}, NOW(), NOW())
        </foreach>
    </insert>

    <!-- 批量更新订单状态 -->
    <update id="batchUpdateOrderStatus">
        UPDATE orders 
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="orderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 动态条件查询订单 -->
    <select id="findOrdersByCondition" parameterType="Order" resultMap="orderResultMap">
        SELECT <include refid="orderColumns"/>
        FROM orders
        <include refid="orderWhereCondition"/>
        ORDER BY order_date DESC
    </select>

    <!-- 复杂条件查询订单 -->
    <select id="findOrdersByComplexCondition" resultMap="orderResultMap">
        SELECT <include refid="orderColumns"/>
        FROM orders
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND status IN
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="startDate != null">
                AND order_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND order_date &lt;= #{endDate}
            </if>
            <if test="minAmount != null">
                AND total_amount >= #{minAmount}
            </if>
            <if test="maxAmount != null">
                AND total_amount &lt;= #{maxAmount}
            </if>
        </where>
        ORDER BY order_date DESC
    </select>

    <!-- 使用set标签的动态更新 -->
    <update id="updateOrderSelective" parameterType="Order">
        UPDATE orders
        <set>
            <if test="orderNumber != null and orderNumber != ''">
                order_number = #{orderNumber},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="orderDate != null">
                order_date = #{orderDate},
            </if>
            updated_at = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 复杂查询示例：查询用户的订单统计信息 -->
    <select id="getUserOrderStatistics" parameterType="Long" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_orders,
            COALESCE(SUM(CASE WHEN status != 'CANCELLED' THEN total_amount ELSE 0 END), 0) as total_amount,
            COALESCE(AVG(CASE WHEN status != 'CANCELLED' THEN total_amount ELSE NULL END), 0) as avg_order_amount,
            MAX(order_date) as last_order_date,
            MIN(order_date) as first_order_date
        FROM orders
        WHERE user_id = #{userId}
    </select>

    <!-- 使用choose-when-otherwise的动态查询 -->
    <select id="findOrdersByStatusType" resultMap="orderResultMap">
        SELECT <include refid="orderColumns"/>
        FROM orders
        <where>
            <choose>
                <when test="statusType == 'active'">
                    status IN ('PENDING', 'CONFIRMED', 'PAID', 'SHIPPED')
                </when>
                <when test="statusType == 'completed'">
                    status IN ('DELIVERED', 'COMPLETED')
                </when>
                <when test="statusType == 'inactive'">
                    status IN ('CANCELLED', 'REFUNDED')
                </when>
                <otherwise>
                    1 = 1
                </otherwise>
            </choose>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
        </where>
        ORDER BY order_date DESC
    </select>

    <!-- 使用bind标签的查询 -->
    <select id="searchOrdersByKeyword" resultMap="orderResultMap">
        <bind name="pattern" value="'%' + keyword + '%'" />
        SELECT <include refid="orderColumns"/>
        FROM orders
        WHERE order_number LIKE #{pattern}
        ORDER BY order_date DESC
    </select>

    <!-- 使用trim标签的动态查询 -->
    <select id="findOrdersByMultipleConditions" resultMap="orderResultMap">
        SELECT <include refid="orderColumns"/>
        FROM orders
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="orderNumber != null and orderNumber != ''">
                AND order_number LIKE CONCAT('%', #{orderNumber}, '%')
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startDate != null">
                AND order_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND order_date &lt;= #{endDate}
            </if>
        </trim>
        ORDER BY order_date DESC
    </select>

    <!-- 复杂统计查询：月度订单统计 -->
    <select id="getMonthlyOrderStatistics" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(order_date, '%Y-%m') as month,
            COUNT(*) as order_count,
            SUM(CASE WHEN status != 'CANCELLED' THEN total_amount ELSE 0 END) as total_revenue,
            AVG(CASE WHEN status != 'CANCELLED' THEN total_amount ELSE NULL END) as avg_order_value,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_count
        FROM orders
        <where>
            <if test="startDate != null">
                AND order_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND order_date &lt;= #{endDate}
            </if>
        </where>
        GROUP BY DATE_FORMAT(order_date, '%Y-%m')
        ORDER BY month DESC
    </select>

    <!-- 子查询示例：查询高价值订单 -->
    <select id="findHighValueOrders" resultMap="orderResultMap">
        SELECT <include refid="orderColumns"/>
        FROM orders
        WHERE total_amount > (
            SELECT AVG(total_amount) * #{multiplier}
            FROM orders
            WHERE status != 'CANCELLED'
        )
        AND status != 'CANCELLED'
        ORDER BY total_amount DESC
        LIMIT #{limit}
    </select>

</mapper>
