package com.example.mybatis.mapper;

import com.example.mybatis.entity.Product;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.FetchType;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 产品Mapper接口
 * 演示MyBatis动态SQL和复杂查询
 * 
 * <AUTHOR> Learning
 * @version 1.0
 */
@Mapper
public interface ProductMapper {
    
    // ========== 基础CRUD操作 ==========
    
    /**
     * 插入产品
     */
    @Insert("INSERT INTO products(name, description, price, stock_quantity, category_id, status, created_at, updated_at) " +
            "VALUES(#{name}, #{description}, #{price}, #{stockQuantity}, #{categoryId}, #{status}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertProduct(Product product);
    
    /**
     * 根据ID删除产品
     */
    @Delete("DELETE FROM products WHERE id = #{id}")
    int deleteProductById(Long id);
    
    /**
     * 更新产品信息
     */
    @Update("UPDATE products SET name=#{name}, description=#{description}, price=#{price}, " +
            "stock_quantity=#{stockQuantity}, category_id=#{categoryId}, status=#{status}, " +
            "updated_at=NOW() WHERE id=#{id}")
    int updateProduct(Product product);
    
    /**
     * 根据ID查询产品
     */
    @Select("SELECT * FROM products WHERE id = #{id}")
    Product findProductById(Long id);
    
    /**
     * 查询所有产品
     */
    @Select("SELECT * FROM products ORDER BY created_at DESC")
    List<Product> findAllProducts();
    
    // ========== 关联查询 ==========
    
    /**
     * 查询产品及其分类信息
     */
    @Select("SELECT * FROM products WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id", id = true),
        @Result(property = "name", column = "name"),
        @Result(property = "description", column = "description"),
        @Result(property = "price", column = "price"),
        @Result(property = "stockQuantity", column = "stock_quantity"),
        @Result(property = "categoryId", column = "category_id"),
        @Result(property = "status", column = "status"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at"),
        @Result(property = "category", column = "category_id", 
                one = @One(select = "com.example.mybatis.mapper.CategoryMapper.findCategoryById", 
                          fetchType = FetchType.LAZY))
    })
    Product findProductWithCategory(Long id);
    
    /**
     * 根据分类ID查询产品列表
     */
    @Select("SELECT * FROM products WHERE category_id = #{categoryId} AND status = 'ACTIVE' ORDER BY name")
    List<Product> findProductsByCategoryId(Long categoryId);
    
    /**
     * 查询产品及其订单项信息
     */
    @Select("SELECT * FROM products WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id", id = true),
        @Result(property = "name", column = "name"),
        @Result(property = "description", column = "description"),
        @Result(property = "price", column = "price"),
        @Result(property = "stockQuantity", column = "stock_quantity"),
        @Result(property = "categoryId", column = "category_id"),
        @Result(property = "status", column = "status"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at"),
        @Result(property = "orderItems", column = "id", 
                many = @Many(select = "com.example.mybatis.mapper.OrderItemMapper.findOrderItemsByProductId", 
                           fetchType = FetchType.LAZY))
    })
    Product findProductWithOrderItems(Long id);
    
    // ========== 条件查询 ==========
    
    /**
     * 根据名称模糊查询产品
     */
    @Select("SELECT * FROM products WHERE name LIKE CONCAT('%', #{name}, '%') ORDER BY name")
    List<Product> findProductsByNameLike(String name);
    
    /**
     * 根据状态查询产品
     */
    @Select("SELECT * FROM products WHERE status = #{status} ORDER BY created_at DESC")
    List<Product> findProductsByStatus(Product.ProductStatus status);
    
    /**
     * 根据价格范围查询产品
     */
    @Select("SELECT * FROM products WHERE price BETWEEN #{minPrice} AND #{maxPrice} ORDER BY price")
    List<Product> findProductsByPriceRange(@Param("minPrice") BigDecimal minPrice,
                                          @Param("maxPrice") BigDecimal maxPrice);
    
    /**
     * 查询库存不足的产品
     */
    @Select("SELECT * FROM products WHERE stock_quantity < #{threshold} AND status = 'ACTIVE' ORDER BY stock_quantity")
    List<Product> findLowStockProducts(@Param("threshold") Integer threshold);
    
    // ========== 统计查询 ==========
    
    /**
     * 统计产品总数
     */
    @Select("SELECT COUNT(*) FROM products")
    long countProducts();
    
    /**
     * 根据状态统计产品数量
     */
    @Select("SELECT COUNT(*) FROM products WHERE status = #{status}")
    long countProductsByStatus(Product.ProductStatus status);
    
    /**
     * 统计各状态产品数量
     */
    @Select("SELECT status, COUNT(*) as count FROM products GROUP BY status")
    List<Map<String, Object>> countProductsByStatusGroup();
    
    /**
     * 统计各分类产品数量
     */
    @Select("SELECT c.name as category_name, COUNT(p.id) as product_count " +
            "FROM categories c LEFT JOIN products p ON c.id = p.category_id " +
            "GROUP BY c.id, c.name ORDER BY product_count DESC")
    List<Map<String, Object>> countProductsByCategory();
    
    /**
     * 计算产品总价值
     */
    @Select("SELECT COALESCE(SUM(price * stock_quantity), 0) FROM products WHERE status = 'ACTIVE'")
    BigDecimal calculateTotalProductValue();
    
    /**
     * 计算平均产品价格
     */
    @Select("SELECT COALESCE(AVG(price), 0) FROM products WHERE status = 'ACTIVE'")
    BigDecimal calculateAverageProductPrice();
    
    // ========== 动态查询 ==========
    
    /**
     * 动态条件查询产品
     */
    List<Product> findProductsByCondition(Product condition);
    
    /**
     * 复杂条件查询产品
     */
    List<Product> findProductsByComplexCondition(@Param("name") String name,
                                               @Param("categoryIds") List<Long> categoryIds,
                                               @Param("statusList") List<Product.ProductStatus> statusList,
                                               @Param("minPrice") BigDecimal minPrice,
                                               @Param("maxPrice") BigDecimal maxPrice,
                                               @Param("minStock") Integer minStock,
                                               @Param("maxStock") Integer maxStock);
    
    /**
     * 分页查询产品
     */
    List<Product> findProductsWithPagination(@Param("offset") int offset, 
                                           @Param("limit") int limit,
                                           @Param("orderBy") String orderBy,
                                           @Param("orderDirection") String orderDirection);
    
    // ========== 批量操作 ==========
    
    /**
     * 批量插入产品
     */
    int batchInsertProducts(@Param("products") List<Product> products);
    
    /**
     * 批量更新产品状态
     */
    int batchUpdateProductStatus(@Param("productIds") List<Long> productIds, 
                               @Param("status") Product.ProductStatus status);
    
    /**
     * 批量更新产品库存
     */
    int batchUpdateProductStock(@Param("stockUpdates") List<Map<String, Object>> stockUpdates);
    
    /**
     * 批量删除产品
     */
    @Delete("<script>" +
            "DELETE FROM products WHERE id IN " +
            "<foreach collection='productIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchDeleteProducts(@Param("productIds") List<Long> productIds);
    
    // ========== 库存管理 ==========
    
    /**
     * 减少产品库存
     */
    @Update("UPDATE products SET stock_quantity = stock_quantity - #{quantity}, updated_at = NOW() " +
            "WHERE id = #{productId} AND stock_quantity >= #{quantity}")
    int decreaseStock(@Param("productId") Long productId, @Param("quantity") Integer quantity);
    
    /**
     * 增加产品库存
     */
    @Update("UPDATE products SET stock_quantity = stock_quantity + #{quantity}, updated_at = NOW() " +
            "WHERE id = #{productId}")
    int increaseStock(@Param("productId") Long productId, @Param("quantity") Integer quantity);
    
    /**
     * 检查库存是否充足
     */
    @Select("SELECT stock_quantity >= #{quantity} FROM products WHERE id = #{productId}")
    boolean checkStockAvailable(@Param("productId") Long productId, @Param("quantity") Integer quantity);
    
    // ========== 特殊查询 ==========
    
    /**
     * 查询热销产品（根据订单项数量）
     */
    @Select("SELECT p.*, COUNT(oi.id) as order_count " +
            "FROM products p LEFT JOIN order_items oi ON p.id = oi.product_id " +
            "WHERE p.status = 'ACTIVE' " +
            "GROUP BY p.id " +
            "ORDER BY order_count DESC " +
            "LIMIT #{limit}")
    List<Product> findHotProducts(@Param("limit") Integer limit);
    
    /**
     * 查询推荐产品（同分类下的其他产品）
     */
    @Select("SELECT * FROM products " +
            "WHERE category_id = (SELECT category_id FROM products WHERE id = #{productId}) " +
            "AND id != #{productId} AND status = 'ACTIVE' " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<Product> findRecommendedProducts(@Param("productId") Long productId, @Param("limit") Integer limit);
}
