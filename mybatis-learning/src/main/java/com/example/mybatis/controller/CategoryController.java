package com.example.mybatis.controller;

import com.example.mybatis.entity.Category;
import com.example.mybatis.service.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * 分类控制器
 * 演示MyBatis树形结构和自关联查询的API
 * 
 * <AUTHOR> Learning
 * @version 1.0
 */
@RestController
@RequestMapping("/api/categories")
@RequiredArgsConstructor
@Validated
@Tag(name = "分类管理", description = "分类相关的CRUD操作和树形结构查询API")
public class CategoryController {
    
    private final CategoryService categoryService;
    
    // ========== 基础CRUD操作 ==========
    
    /**
     * 创建分类
     */
    @PostMapping
    @Operation(summary = "创建分类", description = "创建一个新分类")
    public ResponseEntity<Category> createCategory(@Valid @RequestBody Category category) {
        Category createdCategory = categoryService.createCategory(category);
        return ResponseEntity.ok(createdCategory);
    }
    
    /**
     * 根据ID获取分类
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取分类", description = "根据分类ID获取分类信息")
    public ResponseEntity<Category> getCategoryById(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id) {
        Category category = categoryService.getCategoryById(id);
        return ResponseEntity.ok(category);
    }
    
    /**
     * 获取所有分类
     */
    @GetMapping
    @Operation(summary = "获取所有分类", description = "获取系统中所有分类列表")
    public ResponseEntity<List<Category>> getAllCategories() {
        List<Category> categories = categoryService.getAllCategories();
        return ResponseEntity.ok(categories);
    }
    
    /**
     * 更新分类
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新分类", description = "更新分类信息")
    public ResponseEntity<Category> updateCategory(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id,
            @Valid @RequestBody Category category) {
        Category updatedCategory = categoryService.updateCategory(id, category);
        return ResponseEntity.ok(updatedCategory);
    }
    
    /**
     * 删除分类
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除分类", description = "根据分类ID删除分类")
    public ResponseEntity<String> deleteCategory(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id) {
        categoryService.deleteCategory(id);
        return ResponseEntity.ok("分类删除成功");
    }
    
    // ========== 层级查询 ==========
    
    /**
     * 获取根分类列表
     */
    @GetMapping("/root")
    @Operation(summary = "获取根分类", description = "获取所有顶级分类（没有父分类的分类）")
    public ResponseEntity<List<Category>> getRootCategories() {
        List<Category> categories = categoryService.getRootCategories();
        return ResponseEntity.ok(categories);
    }
    
    /**
     * 根据父分类ID获取子分类列表
     */
    @GetMapping("/parent/{parentId}")
    @Operation(summary = "获取子分类", description = "根据父分类ID获取子分类列表")
    public ResponseEntity<List<Category>> getCategoriesByParentId(
            @Parameter(description = "父分类ID") @PathVariable @Min(1) Long parentId) {
        List<Category> categories = categoryService.getCategoriesByParentId(parentId);
        return ResponseEntity.ok(categories);
    }
    
    /**
     * 获取分类及其父分类信息
     */
    @GetMapping("/{id}/with-parent")
    @Operation(summary = "获取分类及父分类", description = "获取分类信息及其父分类信息")
    public ResponseEntity<Category> getCategoryWithParent(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id) {
        Category category = categoryService.getCategoryWithParent(id);
        return ResponseEntity.ok(category);
    }
    
    /**
     * 获取分类及其子分类信息
     */
    @GetMapping("/{id}/with-children")
    @Operation(summary = "获取分类及子分类", description = "获取分类信息及其所有子分类")
    public ResponseEntity<Category> getCategoryWithChildren(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id) {
        Category category = categoryService.getCategoryWithChildren(id);
        return ResponseEntity.ok(category);
    }
    
    /**
     * 获取分类及其产品信息
     */
    @GetMapping("/{id}/with-products")
    @Operation(summary = "获取分类及产品", description = "获取分类信息及其所有产品")
    public ResponseEntity<Category> getCategoryWithProducts(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id) {
        Category category = categoryService.getCategoryWithProducts(id);
        return ResponseEntity.ok(category);
    }
    
    // ========== 树形结构查询 ==========
    
    /**
     * 获取完整的分类树
     */
    @GetMapping("/tree")
    @Operation(summary = "获取分类树", description = "获取完整的分类树形结构")
    public ResponseEntity<List<Category>> getCategoryTree() {
        List<Category> categoryTree = categoryService.getCategoryTree();
        return ResponseEntity.ok(categoryTree);
    }
    
    /**
     * 获取分类的所有祖先分类
     */
    @GetMapping("/{id}/ancestors")
    @Operation(summary = "获取祖先分类", description = "获取指定分类的所有祖先分类")
    public ResponseEntity<List<Category>> getAncestorCategories(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id) {
        List<Category> ancestors = categoryService.getAncestorCategories(id);
        return ResponseEntity.ok(ancestors);
    }
    
    /**
     * 获取分类的所有后代分类
     */
    @GetMapping("/{id}/descendants")
    @Operation(summary = "获取后代分类", description = "获取指定分类的所有后代分类")
    public ResponseEntity<List<Category>> getDescendantCategories(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id) {
        List<Category> descendants = categoryService.getDescendantCategories(id);
        return ResponseEntity.ok(descendants);
    }
    
    /**
     * 获取指定层级的分类
     */
    @GetMapping("/level/{level}")
    @Operation(summary = "按层级获取分类", description = "获取指定层级的所有分类")
    public ResponseEntity<List<Category>> getCategoriesByLevel(
            @Parameter(description = "层级（从1开始）") @PathVariable @Min(1) int level) {
        List<Category> categories = categoryService.getCategoriesByLevel(level);
        return ResponseEntity.ok(categories);
    }
    
    /**
     * 获取分类的层级深度
     */
    @GetMapping("/{id}/level")
    @Operation(summary = "获取分类层级", description = "获取指定分类的层级深度")
    public ResponseEntity<Integer> getCategoryLevel(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id) {
        int level = categoryService.getCategoryLevel(id);
        return ResponseEntity.ok(level);
    }
    
    // ========== 统计查询 ==========
    
    /**
     * 统计分类总数
     */
    @GetMapping("/count")
    @Operation(summary = "统计分类数量", description = "获取系统中分类总数")
    public ResponseEntity<Long> countCategories() {
        long count = categoryService.countCategories();
        return ResponseEntity.ok(count);
    }
    
    /**
     * 统计根分类数量
     */
    @GetMapping("/count/root")
    @Operation(summary = "统计根分类数量", description = "获取根分类（顶级分类）数量")
    public ResponseEntity<Long> countRootCategories() {
        long count = categoryService.countRootCategories();
        return ResponseEntity.ok(count);
    }
    
    /**
     * 根据父分类统计子分类数量
     */
    @GetMapping("/count/parent/{parentId}")
    @Operation(summary = "统计子分类数量", description = "根据父分类ID统计子分类数量")
    public ResponseEntity<Long> countCategoriesByParentId(
            @Parameter(description = "父分类ID") @PathVariable @Min(1) Long parentId) {
        long count = categoryService.countCategoriesByParentId(parentId);
        return ResponseEntity.ok(count);
    }
    
    /**
     * 统计每个分类下的产品数量
     */
    @GetMapping("/count/products")
    @Operation(summary = "统计分类产品数量", description = "统计每个分类下的产品数量")
    public ResponseEntity<List<Map<String, Object>>> getProductCountByCategory() {
        List<Map<String, Object>> result = categoryService.getProductCountByCategory();
        return ResponseEntity.ok(result);
    }
    
    // ========== 查询操作 ==========
    
    /**
     * 根据名称模糊查询分类
     */
    @GetMapping("/search/name")
    @Operation(summary = "按名称搜索分类", description = "根据分类名称进行模糊查询")
    public ResponseEntity<List<Category>> searchCategoriesByName(
            @Parameter(description = "分类名称") @RequestParam String name) {
        List<Category> categories = categoryService.searchCategoriesByName(name);
        return ResponseEntity.ok(categories);
    }
    
    /**
     * 动态条件查询分类
     */
    @PostMapping("/search")
    @Operation(summary = "动态条件查询", description = "根据动态条件查询分类")
    public ResponseEntity<List<Category>> searchCategories(@RequestBody Category condition) {
        List<Category> categories = categoryService.searchCategories(condition);
        return ResponseEntity.ok(categories);
    }
    
    /**
     * 获取叶子分类
     */
    @GetMapping("/leaf")
    @Operation(summary = "获取叶子分类", description = "获取所有叶子分类（没有子分类的分类）")
    public ResponseEntity<List<Category>> getLeafCategories() {
        List<Category> categories = categoryService.getLeafCategories();
        return ResponseEntity.ok(categories);
    }
    
    // ========== 批量操作 ==========
    
    /**
     * 批量创建分类
     */
    @PostMapping("/batch")
    @Operation(summary = "批量创建分类", description = "批量创建多个分类")
    public ResponseEntity<String> batchCreateCategories(@Valid @RequestBody List<Category> categories) {
        List<Category> createdCategories = categoryService.batchCreateCategories(categories);
        return ResponseEntity.ok("成功创建 " + createdCategories.size() + " 个分类");
    }
    
    /**
     * 批量删除分类
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除分类", description = "批量删除多个分类")
    public ResponseEntity<String> batchDeleteCategories(@RequestBody List<Long> categoryIds) {
        categoryService.batchDeleteCategories(categoryIds);
        return ResponseEntity.ok("成功删除 " + categoryIds.size() + " 个分类");
    }
    
    // ========== 特殊操作 ==========
    
    /**
     * 移动分类
     */
    @PutMapping("/{id}/move")
    @Operation(summary = "移动分类", description = "将分类移动到新的父分类下")
    public ResponseEntity<String> moveCategory(
            @Parameter(description = "分类ID") @PathVariable @Min(1) Long id,
            @Parameter(description = "新父分类ID，null表示移动到根级别") @RequestParam(required = false) Long newParentId) {
        categoryService.moveCategory(id, newParentId);
        return ResponseEntity.ok("分类移动成功");
    }
}
