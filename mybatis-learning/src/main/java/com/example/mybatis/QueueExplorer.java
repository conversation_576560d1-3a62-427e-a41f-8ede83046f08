package com.example.mybatis;

import java.util.*;
import java.util.concurrent.*;
import java.util.Scanner;

public class QueueExplorer {

    public static void main(String[] args) throws Exception {
        Scanner scanner = new Scanner(System.in);

        while (true) {
            System.out.println("\n=== Java 队列测试器 ===");
            System.out.println("1. LinkedList（FIFO 队列）");
            System.out.println("2. ArrayDeque（双端队列）");
            System.out.println("3. PriorityQueue（优先级）");
            System.out.println("4. LinkedBlockingQueue（阻塞队列）");
            System.out.println("5. DelayQueue（延迟队列）");
            System.out.println("0. 退出");

            System.out.print("请输入选择（0-5）：");
            int choice = scanner.nextInt();

            switch (choice) {
                case 1: testLinkedList(); break;
                case 2: testArrayDeque(); break;
                case 3: testPriorityQueue(); break;
                case 4: testLinkedBlockingQueue(); break;
                case 5: testDelayQueue(); break;
                case 0: System.out.println("退出程序"); return;
                default: System.out.println("无效输入，请重新选择！");
            }
        }
    }

    static void testLinkedList() {
        System.out.println("\n▶ 测试 LinkedList 队列（FIFO）");
        Queue<String> queue = new LinkedList<>();
        queue.offer("A");
        queue.offer("B");
        queue.offer("C");
        while (!queue.isEmpty()) {
            System.out.println("Polled: " + queue.poll());
        }
    }

    static void testArrayDeque() {
        System.out.println("\n▶ 测试 ArrayDeque（双端队列）");
        Deque<String> deque = new ArrayDeque<>();
        deque.addFirst("X");
        deque.addLast("Y");
        deque.addLast("Z");
        while (!deque.isEmpty()) {
            System.out.println("Polled: " + deque.pollLast());
        }
    }

    static void testPriorityQueue() {
        System.out.println("\n▶ 测试 PriorityQueue（最小优先）");
        PriorityQueue<Integer> pq = new PriorityQueue<>(Collections.reverseOrder());  //你可以使用 自定义 Comparator，或者 Java 内置的 Collections.reverseOrder()：
        pq.offer(30);
        pq.offer(10);
        pq.offer(20);
        while (!pq.isEmpty()) {
            System.out.println("Polled: " + pq.poll());
        }
    }

    static void testLinkedBlockingQueue() throws InterruptedException {
        System.out.println("\n▶ 测试 LinkedBlockingQueue（阻塞队列，容量 = 2）");
        BlockingQueue<String> queue = new LinkedBlockingQueue<>(2);
        queue.put("A");
        queue.put("B");
        System.out.println("队列满，等待取出一个元素...");

        new Thread(() -> {
            try {
                Thread.sleep(2000);
                String taken = queue.take();
                System.out.println("✔ 异步线程取出元素：" + taken);
            } catch (Exception ignored) {}
        }).start();

        queue.put("C");
        System.out.println("放入了 C");
        while (!queue.isEmpty()) {
            System.out.println("Polled: " + queue.take());
        }
    }

    static void testDelayQueue() throws InterruptedException {
        System.out.println("\n▶ 测试 DelayQueue（延迟队列）");
        DelayQueue<DelayedTask> delayQueue = new DelayQueue<>();
        delayQueue.put(new DelayedTask("任务-A", 2));
        delayQueue.put(new DelayedTask("任务-B", 1));
        delayQueue.put(new DelayedTask("任务-C", 3));

        while (!delayQueue.isEmpty()) {
            System.out.println("Take: " + delayQueue.take().name);
        }
    }

    static class DelayedTask implements Delayed {
        String name;
        long expireTime;

        DelayedTask(String name, long delaySec) {
            this.name = name;
            this.expireTime = System.currentTimeMillis() + delaySec * 1000;
        }

        @Override
        public long getDelay(TimeUnit unit) {
            long delay = expireTime - System.currentTimeMillis();
            return unit.convert(delay, TimeUnit.MILLISECONDS);
        }

        @Override
        public int compareTo(Delayed o) {
            return Long.compare(this.expireTime, ((DelayedTask) o).expireTime);
        }
    }
}
