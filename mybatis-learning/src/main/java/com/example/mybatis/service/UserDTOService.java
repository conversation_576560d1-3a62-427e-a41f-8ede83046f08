package com.example.mybatis.service;

import com.example.mybatis.dto.UserWithOrdersDTO;
import com.example.mybatis.entity.Order;
import com.example.mybatis.entity.User;
import com.example.mybatis.mapper.OrderMapper;
import com.example.mybatis.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户DTO服务类
 * 负责Entity到DTO的转换，保持懒加载优势
 * 
 * <AUTHOR> Learning
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserDTOService {
    
    private final UserMapper userMapper;
    private final OrderMapper orderMapper;
    
    /**
     * 获取用户及订单信息DTO
     * 使用懒加载策略，只在需要时加载订单信息
     */
    public UserWithOrdersDTO getUserWithOrders(Long userId) {
        log.info("获取用户及订单信息DTO: {}", userId);
        
        // 1. 获取用户基础信息（不使用懒加载关联）
        User user = userMapper.findUserById(userId);
        if (user == null) {
            return null;
        }
        
        // 2. 懒加载订单信息（只在需要时查询）
        List<Order> orders = orderMapper.findOrdersByUserId(userId);
        
        // 3. 转换为DTO
        return UserWithOrdersDTO.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .fullName(user.getFullName())
                .age(user.getAge())
                .status(user.getStatus())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .orders(convertToOrderSummaryDTOs(orders))
                .totalOrders(orders.size())
                .totalAmount(calculateTotalAmount(orders))
                .build();
    }
    
    /**
     * 转换订单为摘要DTO
     */
    private List<UserWithOrdersDTO.OrderSummaryDTO> convertToOrderSummaryDTOs(List<Order> orders) {
        return orders.stream()
                .map(order -> UserWithOrdersDTO.OrderSummaryDTO.builder()
                        .id(order.getId())
                        .orderNumber(order.getOrderNumber())
                        .totalAmount(order.getTotalAmount().doubleValue())
                        .status(order.getStatus().name())
                        .orderDate(order.getOrderDate())
                        .itemCount(getOrderItemCount(order.getId()))
                        .build())
                .collect(Collectors.toList());
    }
    
    /**
     * 懒加载获取订单项数量
     */
    private Integer getOrderItemCount(Long orderId) {
        // 这里可以使用更高效的count查询，而不是加载所有订单项
        return orderMapper.countOrderItemsByOrderId(orderId);
    }
    
    /**
     * 计算订单总金额
     */
    private Double calculateTotalAmount(List<Order> orders) {
        return orders.stream()
                .mapToDouble(order -> order.getTotalAmount().doubleValue())
                .sum();
    }
    
    /**
     * 获取所有用户及订单信息DTO列表
     */
    public List<UserWithOrdersDTO> getAllUsersWithOrders() {
        log.info("获取所有用户及订单信息DTO");
        
        // 1. 获取所有用户（不使用懒加载关联）
        List<User> users = userMapper.findAllUsers();
        
        // 2. 为每个用户懒加载订单信息
        return users.stream()
                .map(user -> getUserWithOrders(user.getId()))
                .collect(Collectors.toList());
    }
}
