# elasticsearch.yml \u6587\u4EF6\u4E2D\u7684 cluster.name
spring.data.elasticsearch.cluster-name=docker-cluster
# elasticsearch \u8C03\u7528\u5730\u5740\uFF0C\u591A\u4E2A\u4F7F\u7528\u201C,\u201D\u9694\u5F00
spring.data.elasticsearch.cluster-nodes=127.0.0.1:9300
#spring.data.elasticsearch.repositories.enabled=true
#spring.data.elasticsearch.username=elastic
#spring.data.elasticsearch.password=123456
#spring.data.elasticsearch.network.host=0.0.0.0
