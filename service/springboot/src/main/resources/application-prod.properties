spring.application.name=captcha-service
server.port=8080
server.servlet.context-path=/captcha-api

# å¯¹äºåå¸å¼é¨ç½²çåºç¨ï¼æä»¬å»ºè®®åºç¨èªå·±å®ç°CaptchaCacheServiceï¼æ¯å¦ç¨Redisæèmemcacheï¼
# åèCaptchaCacheServiceRedisImpl.java
# å¦æåºç¨æ¯åç¹çï¼ä¹æ²¡æä½¿ç¨redisï¼é£é»è®¤ä½¿ç¨åå­ã
# åå­ç¼å­åªéååèç¹é¨ç½²çåºç¨ï¼å¦åéªè¯ç çäº§ä¸éªè¯å¨èç¹ä¹é´ä¿¡æ¯ä¸åæ­¥ï¼å¯¼è´å¤±è´¥ã
# ï¼ï¼ï¼ æ³¨æå¦ï¼å¦æåºç¨æä½¿ç¨spring-boot-starter-data-redisï¼
# è¯·æå¼CaptchaCacheServiceRedisImpl.javaæ³¨éã
aj.captcha.cache-type=local
# localç¼å­çéå¼,è¾¾å°è¿ä¸ªå¼ï¼æ¸é¤ç¼å­
aj.captcha.cache-number=1000
# localå®æ¶æ¸é¤è¿æç¼å­(åä½ç§),è®¾ç½®ä¸º0ä»£è¡¨ä¸æ§è¡
aj.captcha.timing-clear=3600
#spring.redis.host=************
#spring.redis.port=6379
#spring.redis.password=
#spring.redis.database=2
#spring.redis.timeout=6000

aj.captcha.jigsaw=classpath:images/jigsaw
aj.captcha.pic-click=classpath:images/pic-click
# éªè¯ç ç±»ådefaultä¸¤ç§é½å®ä¾åã
aj.captcha.type=default
# æ±å­ç»ä¸ä½¿ç¨Unicode,ä¿è¯ç¨åºéè¿@valueè¯»åå°æ¯ä¸­æï¼å¯éè¿è¿ä¸ªå¨çº¿è½¬æ¢
# https://tool.chinaz.com/tools/unicode.aspx ä¸­æè½¬Unicode
# å³ä¸è§æ°´å°æå­(æçæ°´å°)
aj.captcha.water-mark=\u6211\u7684\u6c34\u5370
# å³ä¸è§æ°´å°å­ä½(å®ä½)
aj.captcha.water-font=\u5b8b\u4f53
# ç¹éæå­éªè¯ç çæå­å­ä½(å®ä½)
aj.captcha.font-type=\u5b8b\u4f53
# æ ¡éªæ»å¨æ¼å¾åè®¸è¯¯å·®åç§»é(é»è®¤5åç´ )
aj.captcha.slip-offset=5
# aeså å¯åæ å¼å¯æèç¦ç¨(true|false)
aj.captcha.aes-status=true
# æ»å¨å¹²æ°é¡¹(0/1/2)
aj.captcha.interference-options=1