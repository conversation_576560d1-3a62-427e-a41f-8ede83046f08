import React from 'react';
import { Layout, Typography } from 'antd';
import { UserStatsChart } from '../components/UserStatsChart';
import { OrderStatsChart } from '../components/OrderStatsChart';
import { ProductSalesChart } from '../components/ProductSalesChart';
import { TimeTrendChart } from '../components/TimeTrendChart';
import { GeographicHeatmap } from '../components/GeographicHeatmap';
import { RevenuePrediction } from '../components/RevenuePrediction';

const { Content, Header } = Layout;
const { Title } = Typography;

export const Dashboard: React.FC = () => {
  return (
    <Layout style={{ 
      minHeight: '100vh', 
      background: '#f8fafc'
    }}>
      <Header style={{ 
        background: 'rgba(255,255,255,0.98)', 
        padding: '0 32px', 
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        backdropFilter: 'blur(20px)',
        borderBottom: '1px solid rgba(0,0,0,0.06)',
        height: '80px',
        lineHeight: '80px',
        position: 'sticky',
        top: 0,
        zIndex: 100
      }}>
        <div style={{ display: 'flex', alignItems: 'center', height: '80px' }}>
          <div style={{ fontSize: '32px', marginRight: '16px' }}>📊</div>
          <Title level={2} style={{ 
            margin: 0, 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 700
          }}>
            Cube.js 数据分析仪表板
          </Title>
          <div style={{ marginLeft: 'auto', color: '#666', fontSize: '16px' }}>
            实时数据监控 • 现代化分析平台
          </div>
        </div>
      </Header>
      
      <Content style={{ 
        padding: '80px 0',
        background: 'transparent'
      }}>
        <div className="dashboard-container">
          <div className="dashboard-content">
            <div className="dashboard-grid">
              <div className="chart-card">
                <UserStatsChart />
              </div>
              <div className="chart-card">
                <OrderStatsChart />
              </div>
              <div className="chart-card">
                <ProductSalesChart />
              </div>
              <div className="chart-card">
                <TimeTrendChart />
              </div>
              <div className="chart-card">
                <GeographicHeatmap />
              </div>
              <div className="chart-card">
                <RevenuePrediction />
              </div>
              
              <div className="summary-card">
                <div style={{ 
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%)', 
                  padding: '48px', 
                  borderRadius: '24px',
                  textAlign: 'center',
                  minHeight: '160px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px solid rgba(255,255,255,0.3)',
                  backdropFilter: 'blur(20px)',
                  boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
                  transition: 'all 0.3s ease'
                }}>
                  <div style={{ fontSize: '56px', marginBottom: '24px' }}>🎉</div>
                  <Title level={3} style={{ 
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    marginBottom: '12px',
                    fontWeight: 600
                  }}>
                    完整的数据分析平台已就绪！
                  </Title>
                  <p style={{ color: '#666', fontSize: '16px', margin: 0, lineHeight: '1.6' }}>
                    基于 React + TypeScript + Cube.js 构建<br/>
                    现代化商业智能解决方案
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Content>
    </Layout>
  );
}; 