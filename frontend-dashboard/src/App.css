/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    '<PERSON>bunt<PERSON>', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  background: #f8fafc;
}

#root {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* 布局样式 */
.ant-layout {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
  background: #f8fafc;
}

.ant-layout-header {
  margin: 0;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
}

.ant-layout-content {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: calc(100vh - 80px);
}

/* Dashboard容器 - 背景区域，使用百分比宽度 */
.dashboard-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  padding: 48px;
  margin: 0 auto;
  width: 90%;
  min-width: 1300px;
  max-width: 1600px;
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
  position: relative;
}

/* Dashboard内容区域 - 固定宽度居中 */
.dashboard-content {
  width: 1120px;
  margin: 0 auto;
}

/* 现代化Dashboard网格布局 */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  width: 100%;
  box-sizing: border-box;
}

/* 图表卡片样式 */
.chart-card {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform: translateY(0);
}

.chart-card:hover {
  transform: translateY(-8px);
}

.chart-card .ant-card {
  border: none;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
}

.chart-card .ant-card:hover {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.98);
}

.chart-card .ant-card-head {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px 20px 0 0;
  padding: 20px 24px;
}

.chart-card .ant-card-body {
  padding: 24px;
}

/* 总结卡片样式 */
.summary-card {
  grid-column: 1 / -1;
  margin-top: 20px;
}

.summary-card:hover div {
  transform: translateY(-4px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
}

/* 标题样式重置 */
.ant-typography {
  margin-bottom: 0;
}

.ant-typography h1,
.ant-typography h2,
.ant-typography h3,
.ant-typography h4 {
  margin-top: 0;
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-container {
    width: 95%;
    min-width: 1000px;
    padding: 40px 0;
  }
  
  .dashboard-content {
    width: 940px;
    padding: 0 40px;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr 1fr;
    gap: 32px;
  }
  
  .ant-layout-content {
    padding: 60px 0 !important;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    width: 95%;
    max-width: calc(100% - 20px);
    padding: 32px;
    margin: 0 auto;
    border-radius: 20px;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .ant-layout-content {
    padding: 40px 0 !important;
  }
  
  .ant-layout-header {
    height: 64px !important;
    line-height: 64px !important;
  }
  
  .ant-layout-header div {
    height: 64px !important;
  }
  
  .chart-card .ant-card {
    border-radius: 16px;
  }
  
  .summary-card div {
    border-radius: 16px !important;
    padding: 32px !important;
    min-height: 120px !important;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    width: 95%;
    max-width: calc(100% - 20px);
    margin: 0 auto;
    padding: 24px;
    border-radius: 16px;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .ant-layout-content {
    padding: 30px 0 !important;
  }
}
