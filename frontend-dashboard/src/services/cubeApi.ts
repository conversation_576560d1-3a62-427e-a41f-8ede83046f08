import cubejs from '@cubejs-client/core';

// Cube.js API 客户端配置
const cubejsApi = cubejs('', {
  apiUrl: 'http://localhost:4000/cubejs-api/v1'
});

// 用户相关查询
export const getUserStats = () => {
  return cubejsApi.load({
    measures: ['users.count'],
    dimensions: ['users.state']
  });
};

export const getUserGrowth = () => {
  return cubejsApi.load({
    measures: ['users.count'],
    timeDimensions: [{
      dimension: 'users.created_at',
      granularity: 'month'
    }]
  });
};

// 订单相关查询
export const getOrderStats = () => {
  return cubejsApi.load({
    measures: ['orders.count', 'orders.total'],
    dimensions: ['orders.status']
  });
};

export const getOrderTrends = () => {
  return cubejsApi.load({
    measures: ['orders.count', 'orders.total'],
    timeDimensions: [{
      dimension: 'orders.created_at',
      granularity: 'day',
      dateRange: 'last 30 days'
    }]
  });
};

// 产品相关查询
export const getProductStats = () => {
  return cubejsApi.load({
    measures: ['products.count', 'products.price'],
    dimensions: ['products.category']
  });
};

export const getTopProducts = () => {
  return cubejsApi.load({
    measures: ['order_items.quantity'],
    dimensions: ['products.name'],
    order: {
      'order_items.quantity': 'desc'
    },
    limit: 10
  });
};

// 销售相关查询
export const getSalesRevenue = () => {
  return cubejsApi.load({
    measures: ['order_items.price'],
    dimensions: ['products.category'],
    timeDimensions: [{
      dimension: 'orders.created_at',
      granularity: 'month'
    }]
  });
};

// 综合仪表板数据
export const getDashboardData = () => {
  return Promise.all([
    getUserStats(),
    getOrderStats(),
    getProductStats(),
    getOrderTrends()
  ]);
};

// 通用查询方法
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const executeQuery = (query: any) => {
  return cubejsApi.load(query);
};

export default cubejsApi; 