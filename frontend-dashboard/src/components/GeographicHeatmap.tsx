import React, { useState, useEffect } from 'react';
import { ComposedChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Card, Spin, Alert } from 'antd';
import { getUserStats } from '../services/cubeApi';

interface GeographicData {
  region: string;
  users: number;
  orders: number;
  revenue: number;
  intensity: number;
}

export const GeographicHeatmap: React.FC = () => {
  const [data, setData] = useState<GeographicData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await getUserStats();
        
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let geoData = result.tablePivot().map((row: any) => {
          const users = parseInt(row['users.count'] || '0');
          return {
            region: row['users.state'] || '未知地区',
            users: users,
            orders: Math.round(users * (1.5 + Math.random() * 2)), // 模拟订单数
            revenue: Math.round(users * (2000 + Math.random() * 3000)), // 模拟收入
            intensity: users
          };
        });

        // 如果没有数据，显示示例数据
        if (geoData.length === 0 || geoData.every(item => item.users === 0)) {
          geoData = [
            { region: '广东', users: 285, orders: 421, revenue: 1250000, intensity: 285 },
            { region: '北京', users: 198, orders: 312, revenue: 980000, intensity: 198 },
            { region: '上海', users: 176, orders: 289, revenue: 890000, intensity: 176 },
            { region: '浙江', users: 143, orders: 218, revenue: 670000, intensity: 143 },
            { region: '江苏', users: 132, orders: 201, revenue: 610000, intensity: 132 },
            { region: '四川', users: 98, orders: 142, revenue: 420000, intensity: 98 },
            { region: '湖北', users: 87, orders: 125, revenue: 380000, intensity: 87 },
            { region: '陕西', users: 76, orders: 108, revenue: 340000, intensity: 76 },
            { region: '天津', users: 65, orders: 92, revenue: 290000, intensity: 65 }
          ];
          setError('数据获取失败，显示示例数据');
        }

        // 按用户数量排序
        geoData.sort((a, b) => b.users - a.users);
        
        setData(geoData);
      } catch (err) {
        console.error('地理数据获取错误:', err);
        // 发生错误时显示示例数据
        setData([
          { region: '广东', users: 285, orders: 421, revenue: 1250000, intensity: 285 },
          { region: '北京', users: 198, orders: 312, revenue: 980000, intensity: 198 },
          { region: '上海', users: 176, orders: 289, revenue: 890000, intensity: 176 },
          { region: '浙江', users: 143, orders: 218, revenue: 670000, intensity: 143 },
          { region: '江苏', users: 132, orders: 201, revenue: 610000, intensity: 132 },
          { region: '四川', users: 98, orders: 142, revenue: 420000, intensity: 98 },
          { region: '湖北', users: 87, orders: 125, revenue: 380000, intensity: 87 },
          { region: '陕西', users: 76, orders: 108, revenue: 340000, intensity: 76 },
          { region: '天津', users: 65, orders: 92, revenue: 290000, intensity: 65 }
        ]);
        setError('数据获取失败，显示示例数据');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);



  if (loading) {
    return (
      <Card 
        title="🌍 地理位置热力图" 
        style={{ 
          height: '480px',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
        }}
        headStyle={{ 
          background: 'linear-gradient(135deg, #00b894 0%, #00cec9 100%)',
          color: 'white',
          borderRadius: '12px 12px 0 0'
        }}
      >
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title="🌍 地理位置热力图" 
      style={{ 
        height: '480px',
        borderRadius: '12px',
        boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
      }}
      headStyle={{ 
        background: 'linear-gradient(135deg, #00b894 0%, #00cec9 100%)',
        color: 'white',
        borderRadius: '12px 12px 0 0'
      }}
    >
      {error && (
        <Alert 
          message="数据提示" 
          description={error} 
          type="info" 
          showIcon 
          style={{ marginBottom: '16px' }}
        />
      )}
      
      <ResponsiveContainer width="100%" height={error ? 360 : 400}>
        <ComposedChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="region" 
            stroke="#666" 
            fontSize={11}
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis stroke="#666" fontSize={12} />
          <Tooltip 
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e8e8e8',
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
            }}
            formatter={(value: number, name: string) => {
              if (name === 'users') return [`${value} 位用户`, '用户数'];
              if (name === 'orders') return [`${value} 个订单`, '订单数'];
              if (name === 'revenue') return [`¥${value.toLocaleString()}`, '收入'];
              return [value, name];
            }}
          />
          <Legend />
          
          <Bar 
            dataKey="users" 
            name="用户数"
            fill="#00b894"
            radius={[4, 4, 0, 0]}
          />
        </ComposedChart>
      </ResponsiveContainer>
      
      <div style={{ 
        marginTop: '10px', 
        display: 'flex', 
        justifyContent: 'center',
        alignItems: 'center',
        fontSize: '12px',
        color: '#666'
      }}>
        <span style={{ marginRight: '20px' }}>热力强度：</span>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div style={{ width: '12px', height: '12px', backgroundColor: '#74b9ff', borderRadius: '2px' }}></div>
          <span>低</span>
          <div style={{ width: '12px', height: '12px', backgroundColor: '#fdcb6e', borderRadius: '2px' }}></div>
          <span>中</span>
          <div style={{ width: '12px', height: '12px', backgroundColor: '#d63031', borderRadius: '2px' }}></div>
          <span>高</span>
        </div>
      </div>
    </Card>
  );
}; 