import React, { useState, useEffect } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Card, Spin, Alert } from 'antd';
import { getTopProducts } from '../services/cubeApi';

interface ProductSalesData {
  name: string;
  sales: number;
  revenue: number;
}

export const ProductSalesChart: React.FC = () => {
  const [data, setData] = useState<ProductSalesData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await getTopProducts();
        
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let productData = result.tablePivot().map((row: any) => ({
          name: row['products.name'] || '未知产品',
          sales: parseInt(row['order_items.quantity'] || '0'),
          revenue: parseFloat(row['order_items.price'] || '0')
        }));

        // 如果没有数据，显示示例数据
        if (productData.length === 0 || productData.every(item => item.sales === 0)) {
          productData = [
            { name: '苹果手机', sales: 25, revenue: 149975 },
            { name: '华为笔记本', sales: 18, revenue: 161982 },
            { name: '智能手表', sales: 15, revenue: 29985 },
            { name: '蓝牙耳机', sales: 12, revenue: 3588 },
            { name: '小米电视', sales: 10, revenue: 29990 },
            { name: '护肤套装', sales: 8, revenue: 4792 },
            { name: '健身器材', sales: 6, revenue: 7794 },
            { name: '登山包', sales: 5, revenue: 1995 }
          ];
          setError('数据获取失败，显示示例数据');
        }

        // 按销量降序排序，取前8名
        productData = productData
          .sort((a, b) => b.sales - a.sales)
          .slice(0, 8);
        
        setData(productData);
      } catch (err) {
        console.error('产品销售数据获取错误:', err);
        // 发生错误时显示示例数据
        setData([
          { name: '苹果手机', sales: 25, revenue: 149975 },
          { name: '华为笔记本', sales: 18, revenue: 161982 },
          { name: '智能手表', sales: 15, revenue: 29985 },
          { name: '蓝牙耳机', sales: 12, revenue: 3588 },
          { name: '小米电视', sales: 10, revenue: 29990 },
          { name: '护肤套装', sales: 8, revenue: 4792 },
          { name: '健身器材', sales: 6, revenue: 7794 },
          { name: '登山包', sales: 5, revenue: 1995 }
        ]);
        setError('数据获取失败，显示示例数据');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card 
        title="🎯 产品销售排行榜" 
        style={{ 
          height: '480px',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
        }}
        headStyle={{ 
          background: 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)',
          color: 'white',
          borderRadius: '12px 12px 0 0'
        }}
      >
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title="🎯 产品销售排行榜" 
      style={{ 
        height: '480px',
        borderRadius: '12px',
        boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
      }}
      headStyle={{ 
        background: 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)',
        color: 'white',
        borderRadius: '12px 12px 0 0'
      }}
    >
      {error && (
        <Alert 
          message="数据提示" 
          description={error} 
          type="info" 
          showIcon 
          style={{ marginBottom: '16px' }}
        />
      )}
      
      <ResponsiveContainer width="100%" height={error ? 360 : 400}>
        <BarChart
          layout="horizontal"
          data={data}
          margin={{ top: 20, right: 30, left: 80, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis type="number" stroke="#666" fontSize={12} />
          <YAxis 
            type="category" 
            dataKey="name" 
            stroke="#666" 
            fontSize={11}
            width={70}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e8e8e8',
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
            }}
            formatter={(value: number, name: string) => [
              name === 'sales' ? `${value} 件` : `¥${value.toLocaleString()}`,
              name === 'sales' ? '销量' : '销售额'
            ]}
          />
          <Legend />
          <Bar 
            dataKey="sales" 
            fill="url(#salesGradient)" 
            name="销量"
            radius={[0, 4, 4, 0]}
          />
          <defs>
            <linearGradient id="salesGradient" x1="0" y1="0" x2="1" y2="0">
              <stop offset="5%" stopColor="#ffeaa7" stopOpacity={0.9}/>
              <stop offset="95%" stopColor="#fab1a0" stopOpacity={0.7}/>
            </linearGradient>
          </defs>
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );
}; 