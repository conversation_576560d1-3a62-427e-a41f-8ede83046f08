import React, { useState, useEffect } from 'react';
import { Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { Card, Spin, Alert } from 'antd';
import { getOrderTrends } from '../services/cubeApi';

interface TimeTrendData {
  date: string;
  orders: number;
  revenue: number;
}

export const TimeTrendChart: React.FC = () => {
  const [data, setData] = useState<TimeTrendData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await getOrderTrends();
        
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let trendData = result.tablePivot().map((row: any) => ({
          date: row['orders.created_at'] || '未知日期',
          orders: parseInt(row['orders.count'] || '0'),
          revenue: parseFloat(row['orders.total'] || '0')
        }));

        // 如果没有数据，显示示例数据
        if (trendData.length === 0 || trendData.every(item => item.orders === 0)) {
          // 生成最近30天的示例数据
          const today = new Date();
          trendData = [];
          
          for (let i = 29; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            
            // 模拟业务波动
            const baseOrders = 2 + Math.sin(i / 7) * 1.5; // 周期性波动
            const randomFactor = 0.5 + Math.random(); // 随机因素
            const orders = Math.max(0, Math.round(baseOrders * randomFactor));
            const avgOrderValue = 1500 + Math.random() * 1000;
            
            trendData.push({
              date: dateStr,
              orders: orders,
              revenue: Math.round(orders * avgOrderValue)
            });
          }
          setError('数据获取失败，显示示例数据');
        }

        // 按日期排序
        trendData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        
        setData(trendData);
      } catch (err) {
        console.error('时间趋势数据获取错误:', err);
        // 发生错误时显示示例数据
        const today = new Date();
        const exampleData = [];
        
        for (let i = 29; i >= 0; i--) {
          const date = new Date(today);
          date.setDate(date.getDate() - i);
          const dateStr = date.toISOString().split('T')[0];
          
          const baseOrders = 2 + Math.sin(i / 7) * 1.5;
          const randomFactor = 0.5 + Math.random();
          const orders = Math.max(0, Math.round(baseOrders * randomFactor));
          const avgOrderValue = 1500 + Math.random() * 1000;
          
          exampleData.push({
            date: dateStr,
            orders: orders,
            revenue: Math.round(orders * avgOrderValue)
          });
        }
        
        setData(exampleData);
        setError('数据获取失败，显示示例数据');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card 
        title="📈 时间趋势分析" 
        style={{ 
          height: '480px',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
        }}
        headStyle={{ 
          background: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
          color: 'white',
          borderRadius: '12px 12px 0 0'
        }}
      >
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title="📈 时间趋势分析" 
      style={{ 
        height: '480px',
        borderRadius: '12px',
        boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
      }}
      headStyle={{ 
        background: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
        color: 'white',
        borderRadius: '12px 12px 0 0'
      }}
    >
      {error && (
        <Alert 
          message="数据提示" 
          description={error} 
          type="info" 
          showIcon 
          style={{ marginBottom: '16px' }}
        />
      )}
      
      <ResponsiveContainer width="100%" height={error ? 360 : 400}>
        <AreaChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="date" 
            stroke="#666" 
            fontSize={10}
            tickFormatter={(value) => {
              const date = new Date(value);
              return `${date.getMonth() + 1}/${date.getDate()}`;
            }}
          />
          <YAxis 
            yAxisId="left"
            stroke="#666" 
            fontSize={12}
          />
          <YAxis 
            yAxisId="right"
            orientation="right"
            stroke="#666" 
            fontSize={12}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e8e8e8',
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
            }}
            labelFormatter={(value) => `日期: ${value}`}
            formatter={(value: number, name: string) => [
              name === 'orders' ? `${value} 个订单` : `¥${value.toLocaleString()}`,
              name === 'orders' ? '订单量' : '销售额'
            ]}
          />
          <Legend />
          
          <Area
            yAxisId="right"
            type="monotone"
            dataKey="revenue"
            stackId="1"
            stroke="url(#revenueGradient)"
            fill="url(#revenueAreaGradient)"
            name="销售额"
            strokeWidth={2}
          />
          
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="orders"
            stroke="#74b9ff"
            strokeWidth={3}
            dot={{ fill: '#74b9ff', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#74b9ff', strokeWidth: 2, fill: '#fff' }}
            name="订单量"
          />
          
          <defs>
            <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#0984e3" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#74b9ff" stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="revenueAreaGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#0984e3" stopOpacity={0.3}/>
              <stop offset="95%" stopColor="#74b9ff" stopOpacity={0.05}/>
            </linearGradient>
          </defs>
        </AreaChart>
      </ResponsiveContainer>
    </Card>
  );
}; 