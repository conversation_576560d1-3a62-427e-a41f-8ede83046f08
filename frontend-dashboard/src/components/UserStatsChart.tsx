import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Toolt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Card, Spin, Alert } from 'antd';
import { getUserStats } from '../services/cubeApi';

interface UserStatsData {
  state: string;
  count: number;
}

export const UserStatsChart: React.FC = () => {
  const [data, setData] = useState<UserStatsData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await getUserStats();
        
        // 转换数据格式以适应图表组件
        console.log('用户统计原始数据:', result);
        const rawData = result.tablePivot();
        console.log('用户统计表格数据:', rawData);
        
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let chartData = rawData.map((row: any) => ({
          state: row['users.state'] || row['Users State'] || '未知',
          count: parseInt(row['users.count'] || row['Users Count'] || '0')
        }));
        
        // 如果没有数据，显示示例数据
        if (chartData.length === 0 || chartData.every(item => item.count === 0)) {
          chartData = [
            { state: '北京', count: 15 },
            { state: '上海', count: 12 },
            { state: '广东', count: 18 },
            { state: '浙江', count: 8 },
            { state: '江苏', count: 10 }
          ];
          console.log('使用示例数据:', chartData);
        }
        
        console.log('用户统计图表数据:', chartData);
        setData(chartData);
        setError(null);
      } catch (err) {
        console.error('用户统计数据获取错误:', err);
        // 发生错误时也显示示例数据
        setData([
          { state: '北京', count: 15 },
          { state: '上海', count: 12 },
          { state: '广东', count: 18 },
          { state: '浙江', count: 8 },
          { state: '江苏', count: 10 }
        ]);
        setError('数据获取失败，显示示例数据');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card 
        title="用户地区分布" 
        style={{ 
          height: '450px',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
        }}
        headStyle={{ 
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          borderRadius: '12px 12px 0 0'
        }}
      >
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title="用户地区分布" 
      style={{ 
        height: '450px',
        borderRadius: '12px',
        boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
      }}
      headStyle={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px 12px 0 0'
      }}
    >
      {error && (
        <Alert 
          message="数据提示" 
          description={error} 
          type="info" 
          showIcon 
          style={{ marginBottom: '16px' }}
        />
      )}
      
      <ResponsiveContainer width="100%" height={error ? 310 : 350}>
        <BarChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="state" 
            stroke="#666"
            fontSize={12}
          />
          <YAxis stroke="#666" fontSize={12} />
          <Tooltip 
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e8e8e8',
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
            }}
          />
          <Legend />
          <Bar 
            dataKey="count" 
            fill="url(#colorGradient)" 
            name="用户数量"
            radius={[4, 4, 0, 0]}
          />
          <defs>
            <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#667eea" stopOpacity={0.9}/>
              <stop offset="95%" stopColor="#764ba2" stopOpacity={0.7}/>
            </linearGradient>
          </defs>
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );
}; 