import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { Card, Spin, Alert, Row, Col } from 'antd';
import { getOrderStats } from '../services/cubeApi';

interface OrderStatsData {
  name: string;
  value: number;
  total: number;
  originalStatus: string;
}

// 状态映射
const statusMap: Record<string, string> = {
  'pending': '待处理',
  'processing': '处理中', 
  'shipped': '已发货',
  'delivered': '已完成',
  'cancelled': '已取消'
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

export const OrderStatsChart: React.FC = () => {
  const [data, setData] = useState<OrderStatsData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await getOrderStats();
        
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let rawData = result.tablePivot().map((row: any) => ({
          name: statusMap[row['orders.status']] || row['orders.status'] || '未知状态',
          value: parseInt(row['orders.count'] || '0'),
          total: parseFloat(row['orders.total'] || '0'),
          originalStatus: row['orders.status'] || 'unknown'
        }));

        // 如果没有数据，显示示例数据
        if (rawData.length === 0 || rawData.every(item => item.value === 0)) {
          rawData = [
            { name: '已完成', value: 12, total: 25000, originalStatus: 'delivered' },
            { name: '待处理', value: 5, total: 12000, originalStatus: 'pending' },
            { name: '处理中', value: 2, total: 5000, originalStatus: 'processing' },
            { name: '已发货', value: 1, total: 2000, originalStatus: 'shipped' }
          ];
          setError('数据获取失败，显示示例数据');
        }
        
        setData(rawData);
      } catch (err) {
        console.error('订单统计数据获取错误:', err);
        // 发生错误时显示示例数据
        setData([
          { name: '已完成', value: 12, total: 25000, originalStatus: 'delivered' },
          { name: '待处理', value: 5, total: 12000, originalStatus: 'pending' },
          { name: '处理中', value: 2, total: 5000, originalStatus: 'processing' },
          { name: '已发货', value: 1, total: 2000, originalStatus: 'shipped' }
        ]);
        setError('数据获取失败，显示示例数据');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const totalOrders = data.reduce((sum, item) => sum + item.value, 0);
  const totalAmount = data.reduce((sum, item) => sum + item.total, 0);

  if (loading) {
    return (
      <Card 
        title="订单状态分析"
        style={{ 
          height: '450px',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
        }}
        headStyle={{ 
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          color: 'white',
          borderRadius: '12px 12px 0 0'
        }}
      >
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title="订单状态分析"
      style={{ 
        height: '480px',
        borderRadius: '12px',
        boxShadow: '0 4px 16px rgba(0,0,0,0.08)'
      }}
      headStyle={{ 
        background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        color: 'white',
        borderRadius: '12px 12px 0 0'
      }}
    >
      {error && (
        <Alert 
          message="数据提示" 
          description={error} 
          type="info" 
          showIcon 
          style={{ marginBottom: '16px' }}
        />
      )}

      <Row gutter={16} style={{ marginBottom: 20 }}>
        <Col span={12}>
          <div style={{ 
            textAlign: 'center',
            padding: '16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '8px',
            color: 'white'
          }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalOrders}</div>
            <div style={{ fontSize: '14px', opacity: 0.9 }}>总订单数</div>
          </div>
        </Col>
        <Col span={12}>
          <div style={{ 
            textAlign: 'center',
            padding: '16px',
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            borderRadius: '8px',
            color: 'white'
          }}>
            <div style={{ fontSize: '20px', fontWeight: 'bold' }}>¥{totalAmount.toLocaleString()}</div>
            <div style={{ fontSize: '14px', opacity: 0.9 }}>总金额</div>
          </div>
        </Col>
      </Row>
      
      <ResponsiveContainer width="100%" height={error ? 260 : 300}>
        <PieChart margin={{ top: 5, right: 5, bottom: 40, left: 5 }}>
          <Pie
            data={data}
            cx="50%"
            cy="40%"
            labelLine={false}
            label={false}
            outerRadius={85}
            innerRadius={30}
            fill="#8884d8"
            dataKey="value"
            stroke="#fff"
            strokeWidth={2}
          >
            {data.map((_, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip 
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e8e8e8',
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              fontSize: '14px'
            }}
            formatter={(value: number, name: string, props) => {
              const percentage = ((value / totalOrders) * 100).toFixed(1);
              return [
                `${value} 个订单 (${percentage}%) - ¥${props.payload?.total?.toLocaleString() || 0}`,
                name
              ];
            }}
          />
          <Legend 
            wrapperStyle={{ 
              paddingTop: '10px',
              fontSize: '11px',
              lineHeight: '1.2',
              overflow: 'hidden'
            }}
            iconType="circle"
            layout="horizontal"
            align="center"
            verticalAlign="bottom"
            formatter={(value) => (
              <span style={{ 
                fontSize: '11px', 
                color: '#666',
                display: 'inline-block',
                maxWidth: '60px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {value}
              </span>
            )}
          />
        </PieChart>
      </ResponsiveContainer>
    </Card>
  );
}; 