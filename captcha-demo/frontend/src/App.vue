<template>
  <div id="app">
    <el-container>
      <el-header style="background-color: #409EFF; color: white; text-align: center; line-height: 60px;">
        <h2>🔐 AJ-Captcha 验证码集成示例</h2>
      </el-header>
      
      <el-main>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>🧩 滑动拼图验证码</span>
                </div>
              </template>
              
              <div class="demo-section">
                <el-button type="primary" @click="showSlideVerify = true">
                  打开滑动验证码
                </el-button>
                
                <CaptchaComponent
                  v-if="showSlideVerify"
                  captcha-type="blockPuzzle"
                  @success="onSlideSuccess"
                  @close="showSlideVerify = false"
                />
                
                <div v-if="slideResult" class="result-box">
                  <el-alert
                    title="验证成功！"
                    type="success"
                    :description="`Token: ${slideResult.token}`"
                    show-icon
                    :closable="false"
                  />
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>🎯 文字点选验证码</span>
                </div>
              </template>
              
              <div class="demo-section">
                <el-button type="success" @click="showClickVerify = true">
                  打开点选验证码
                </el-button>
                
                <CaptchaComponent
                  v-if="showClickVerify"
                  captcha-type="clickWord"
                  @success="onClickSuccess"
                  @close="showClickVerify = false"
                />
                
                <div v-if="clickResult" class="result-box">
                  <el-alert
                    title="验证成功！"
                    type="success"
                    :description="`Token: ${clickResult.token}`"
                    show-icon
                    :closable="false"
                  />
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row style="margin-top: 20px;">
          <el-col :span="24">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>📊 验证结果</span>
                </div>
              </template>
              
              <div class="result-section">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="滑动验证状态">
                    <el-tag :type="slideResult ? 'success' : 'info'">
                      {{ slideResult ? '已验证' : '未验证' }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="点选验证状态">
                    <el-tag :type="clickResult ? 'success' : 'info'">
                      {{ clickResult ? '已验证' : '未验证' }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="滑动验证Token" :span="2">
                    {{ slideResult?.token || '暂无' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="点选验证Token" :span="2">
                    {{ clickResult?.token || '暂无' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
      
      <el-footer style="text-align: center; color: #666;">
        <p>🚀 基于 AJ-Captcha 的验证码集成示例 | Vue3 + SpringBoot</p>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import CaptchaComponent from './components/CaptchaComponent.vue'

export default {
  name: 'App',
  components: {
    CaptchaComponent
  },
  data() {
    return {
      showSlideVerify: false,
      showClickVerify: false,
      slideResult: null,
      clickResult: null
    }
  },
  methods: {
    onSlideSuccess(result) {
      this.slideResult = result
      this.showSlideVerify = false
      this.$message.success('滑动验证码验证成功！')
    },
    onClickSuccess(result) {
      this.clickResult = result
      this.showClickVerify = false
      this.$message.success('点选验证码验证成功！')
    }
  }
}
</script>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.demo-section {
  text-align: center;
  padding: 20px 0;
}

.result-box {
  margin-top: 20px;
}

.result-section {
  padding: 10px 0;
}
</style>
