server:
  port: 8090
  servlet:
    context-path: /

spring:
  application:
    name: captcha-demo-backend
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# 日志配置
logging:
  level:
    com.demo.captcha: DEBUG
    com.anji.captcha: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 验证码配置
captcha:
  # 验证码类型 (default支持所有类型)
  type: default
  # 缓存类型
  cacheType: local
  # 水印
  water:
    mark: "Demo"
  # 干扰项
  interference:
    options: 0
  # 滑动误差偏移量 (增加容错范围)
  slip:
    offset: 10
  # 字体配置
  font:
    type: "宋体"
    size: 25
