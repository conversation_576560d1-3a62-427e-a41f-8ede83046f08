package com.example.multithreading;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 多线程学习应用启动类
 * 
 * <AUTHOR> Team
 */
@SpringBootApplication
public class MultithreadingLearningApplication {

    public static void main(String[] args) {
        SpringApplication.run(MultithreadingLearningApplication.class, args);
        System.out.println("🧵 多线程学习应用启动成功！");
        System.out.println("📚 学习内容包括：");
        System.out.println("   1. 基础线程操作");
        System.out.println("   2. 同步机制");
        System.out.println("   3. 线程池");
        System.out.println("   4. 并发工具类");
        System.out.println("   5. 实际应用场景");
    }
}
