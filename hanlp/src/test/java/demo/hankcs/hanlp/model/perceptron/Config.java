/*
 * <author><PERSON><PERSON></author>
 * <email><EMAIL></email>
 * <create-date>2017-10-27 下午5:46</create-date>
 *
 * <copyright file="Config.java" company="码农场">
 * Copyright (c) 2017, 码农场. All Right Reserved, http://www.hankcs.com/
 * This source is subject to <PERSON><PERSON>. Please contact <PERSON><PERSON> to get more information.
 * </copyright>
 */
package demo.hankcs.hanlp.model.perceptron;

import demo.hankcs.hanlp.HanLP;

/**
 * <AUTHOR>
 */
public class Config
{
    public static final String CWS_MODEL_FILE = HanLP.Config.PerceptronCWSModelPath;
    public static final String POS_MODEL_FILE = HanLP.Config.PerceptronPOSModelPath;
    public static final String NER_MODEL_FILE = HanLP.Config.PerceptronNERModelPath;
}
