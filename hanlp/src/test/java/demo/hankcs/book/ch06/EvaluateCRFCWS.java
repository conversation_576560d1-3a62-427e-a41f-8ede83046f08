/*
 * <author><PERSON> He</author>
 * <email><EMAIL></email>
 * <create-date>2018-06-26 3:14 PM</create-date>
 *
 * <copyright file="EvaluateCRFCWS.java">
 * Copyright (c) 2018, <PERSON>. All Rights Reserved, http://www.hankcs.com/
 * This source is subject to <PERSON>. Please contact <PERSON> for more information.
 * </copyright>
 */
package demo.hankcs.book.ch06;

import demo.hankcs.hanlp.corpus.MSR;
import demo.hankcs.hanlp.model.crf.CRFLexicalAnalyzer;
import demo.hankcs.hanlp.model.crf.CRFSegmenter;
import demo.hankcs.hanlp.seg.Segment;
import demo.hankcs.hanlp.seg.common.CWSEvaluator;

import java.io.IOException;

import static demo.hankcs.book.ch06.CrfppTrainHanLPLoad.CRF_MODEL_PATH;

/**
 * 《自然语言处理入门》6.4 HanLP 中的 CRF++ API
 * 配套书籍：http://nlp.hankcs.com/book.php
 * 讨论答疑：https://bbs.hankcs.com/
 *
 * <AUTHOR>
 * @see <a href="http://nlp.hankcs.com/book.php">《自然语言处理入门》</a>
 * @see <a href="https://bbs.hankcs.com/">讨论答疑</a>
 */
public class EvaluateCRFCWS
{
    public static Segment train(String corpus) throws IOException
    {
        CRFSegmenter segmenter = new CRFSegmenter(null);
        segmenter.train(corpus, CRF_MODEL_PATH);
        return new CRFLexicalAnalyzer(segmenter);
        // 训练完毕时，可传入txt格式的模型（不可传入CRF++的二进制模型，不兼容！）
//        return new CRFLexicalAnalyzer(CRF_MODEL_TXT_PATH).enableCustomDictionary(false);
    }

    public static void main(String[] args) throws IOException
    {
        Segment segment = train(MSR.TRAIN_PATH);
        System.out.println(CWSEvaluator.evaluate(segment, MSR.TEST_PATH, MSR.OUTPUT_PATH, MSR.GOLD_PATH, MSR.TRAIN_WORDS)); // 标准化评测
    }
}
