/*
 * <summary></summary>
 * <author>He <PERSON></author>
 * <email><EMAIL></email>
 * <create-date>16/2/13 PM9:08</create-date>
 *
 * <copyright file="ITermFrequencyHolder.java" company="码农场">
 * Copyright (c) 2008-2016, 码农场. All Right Reserved, http://www.hankcs.com/
 * This source is subject to Hankcs. Please contact Hankcs to get more information.
 * </copyright>
 */
package demo.hankcs.hanlp.classification.corpus;

import demo.hankcs.hanlp.classification.collections.FrequencyMap;

/**
 * <AUTHOR>
 */
public interface ITermFrequencyHolder
{
    FrequencyMap<Integer> getTfMap();
}
