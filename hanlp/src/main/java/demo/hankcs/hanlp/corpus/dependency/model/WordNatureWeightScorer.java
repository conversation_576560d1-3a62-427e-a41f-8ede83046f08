/*
 * <summary></summary>
 * <author>He <PERSON></author>
 * <email><EMAIL></email>
 * <create-date>2014/11/20 12:27</create-date>
 *
 * <copyright file="WordNatureWeightScorer.java" company="上海林原信息科技有限公司">
 * Copyright (c) 2003-2014, 上海林原信息科技有限公司. All Right Reserved, http://www.linrunsoft.com/
 * This source is subject to the LinrunSpace License. Please contact 上海林原信息科技有限公司 to get more information.
 * </copyright>
 */
package demo.hankcs.hanlp.corpus.dependency.model;

/**
 * 生成模型打分器，衡量一个边的权值，仅仅利用〈词，词〉，数据稀疏则回退为〈词，词性〉、〈词性，词〉、〈词性，词性〉
 * <AUTHOR>
 */
public class WordNatureWeightScorer
{

}
