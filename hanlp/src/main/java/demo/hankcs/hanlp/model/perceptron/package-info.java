/*
 * <author><PERSON><PERSON></author>
 * <email><EMAIL></email>
 * <create-date>2018-02-28 下午9:44</create-date>
 *
 * <copyright file="package-info.java" company="码农场">
 * Copyright (c) 2018, 码农场. All Right Reserved, http://www.hankcs.com/
 * This source is subject to <PERSON><PERSON>. Please contact <PERSON><PERSON> to get more information.
 * </copyright>
 */
/**
 * 感知机在线学习算法的线性序列标注模型。基于这套框架实现了一整套分词、词性标注和命名实体识别功能。
 * 理论参考邓知龙 《基于感知器算法的高效中文分词与词性标注系统设计与实现》，
 * 简介：http://www.hankcs.com/nlp/segment/implementation-of-word-segmentation-device-java-based-on-structured-average-perceptron.html
 */
package demo.hankcs.hanlp.model.perceptron;