spring.application.name=captcha-service
server.port=8080
server.servlet.context-path=/captcha-api

# 对于分布式部署的应用，我们建议应用自己实现CaptchaCacheService，比如用Redis或者memcache，
# 参考CaptchaCacheServiceRedisImpl.java
# 如果应用是单点的，也没有使用redis，那默认使用内存。
# 内存缓存只适合单节点部署的应用，否则验证码生产与验证在节点之间信息不同步，导致失败。
# ！！！ 注意啦，如果应用有使用spring-boot-starter-data-redis，
# 请打开CaptchaCacheServiceRedisImpl.java注释。
aj.captcha.cache-type=local
# local缓存的阈值,达到这个值，清除缓存
aj.captcha.cache-number=1000
# local定时清除过期缓存(单位秒),设置为0代表不执行
aj.captcha.timing-clear=3600
#spring.redis.host=************
#spring.redis.port=6379
#spring.redis.password=
#spring.redis.database=2
#spring.redis.timeout=6000

aj.captcha.jigsaw=classpath:images/jigsaw
aj.captcha.pic-click=classpath:images/pic-click
# 验证码类型default两种都实例化。
aj.captcha.type=default
# 汉字统一使用Unicode,保证程序通过@value读取到是中文，可通过这个在线转换
# https://tool.chinaz.com/tools/unicode.aspx 中文转Unicode
# 右下角水印文字(我的水印)
aj.captcha.water-mark=\u6211\u7684\u6c34\u5370
# 右下角水印字体(宋体)
aj.captcha.water-font=\u5b8b\u4f53
# 点选文字验证码的文字字体(宋体)
aj.captcha.font-type=\u5b8b\u4f53
# 校验滑动拼图允许误差偏移量(默认5像素)
aj.captcha.slip-offset=5
# aes加密坐标开启或者禁用(true|false)
aj.captcha.aes-status=true
# 滑动干扰项(0/1/2)
aj.captcha.interference-options=1