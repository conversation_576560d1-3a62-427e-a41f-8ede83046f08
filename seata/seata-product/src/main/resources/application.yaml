server:
  port: 8082 # 端口

spring:
  application:
    name: order-product

  datasource:
    url: **********************************************************************************************
    driver-class-name: com.mysql.jdbc.Driver
    username: root
    password: 123456

# Seata 配置项，对应 SeataProperties 类
seata:
  application-id: ${spring.application.name} # Seata 应用编号，默认为 ${spring.application.name}
  tx-service-group: ${spring.application.name}-group # Seata 事务组编号，用于 TC 集群名
  # 服务配置项，对应 ServiceProperties 类
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      order-product-group: default
    # 分组和 Seata 服务的映射
    grouplist:
      default: 127.0.0.1:8091